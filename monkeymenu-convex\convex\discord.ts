import { query, mutation, action } from "./_generated/server";
import { v } from "convex/values";
import { requireAuth, getCurrentUser, requirePermission } from "./lib/permissions";
import { internal } from "./_generated/api";

// Get Discord user by user ID
export const getDiscordUser = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    const user = args.userId ? await ctx.db.get(args.userId) : await getCurrentUser(ctx);
    if (!user) return null;

    return await ctx.db
      .query("discordUsers")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();
  },
});

// Get Discord user by Discord ID
export const getDiscordUserByDiscordId = query({
  args: { discordId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("discordUsers")
      .withIndex("by_discord_id", (q) => q.eq("discordId", args.discordId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();
  },
});

// Get user by Discord ID (for bot commands)
export const getUserByDiscordId = query({
  args: { discordId: v.string() },
  handler: async (ctx, args) => {
    const discordUser = await ctx.db
      .query("discordUsers")
      .withIndex("by_discord_id", (q) => q.eq("discordId", args.discordId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!discordUser) return null;

    return await ctx.db.get(discordUser.userId);
  },
});

// Link Discord account
export const linkDiscordAccount = mutation({
  args: {
    discordId: v.string(),
    username: v.string(),
    discriminator: v.string(),
    avatar: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);

    // Check if Discord account is already linked to another user
    const existingLink = await ctx.db
      .query("discordUsers")
      .withIndex("by_discord_id", (q) => q.eq("discordId", args.discordId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (existingLink && existingLink.userId !== user._id) {
      throw new Error("This Discord account is already linked to another user");
    }

    // Check if user already has Discord linked
    const userDiscordLink = await ctx.db
      .query("discordUsers")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    const now = Date.now();

    if (userDiscordLink) {
      // Update existing link
      return await ctx.db.patch(userDiscordLink._id, {
        discordId: args.discordId,
        username: args.username,
        discriminator: args.discriminator,
        avatar: args.avatar,
        updatedAt: now,
      });
    } else {
      // Create new link
      return await ctx.db.insert("discordUsers", {
        userId: user._id,
        discordId: args.discordId,
        username: args.username,
        discriminator: args.discriminator,
        avatar: args.avatar,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Unlink Discord account
export const unlinkDiscordAccount = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await getCurrentUser(ctx);

    const discordLink = await ctx.db
      .query("discordUsers")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (!discordLink) {
      throw new Error("No Discord account linked");
    }

    return await ctx.db.patch(discordLink._id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// Bot command handler action
export const handleBotCommand = action({
  args: {
    discordId: v.string(),
    command: v.string(),
    args: v.array(v.string()),
    channelId: v.string(),
    guildId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get user from Discord ID
    const user = await ctx.runQuery(internal.discord.getUserByDiscordId, {
      discordId: args.discordId,
    });

    if (!user) {
      return {
        success: false,
        message: "❌ Account not linked. Please link your Torn account first.",
      };
    }

    // Route to appropriate command handler
    switch (args.command.toLowerCase()) {
      case "balance":
        return await ctx.runAction(internal.discord.commands.handleBalanceCommand, {
          userId: user._id,
          discordId: args.discordId,
        });

      case "withdraw":
        return await ctx.runAction(internal.discord.commands.handleWithdrawCommand, {
          userId: user._id,
          discordId: args.discordId,
          amount: parseFloat(args.args[0] || "0"),
        });

      case "targets":
        return await ctx.runAction(internal.discord.commands.handleTargetsCommand, {
          userId: user._id,
          filters: args.args,
        });

      case "war":
      case "wars":
        return await ctx.runAction(internal.discord.commands.handleWarCommand, {
          userId: user._id,
          subcommand: args.args[0],
        });

      case "link":
        return {
          success: true,
          message: "✅ Your account is already linked!",
        };

      case "help":
        return await ctx.runAction(internal.discord.commands.handleHelpCommand, {
          userId: user._id,
        });

      default:
        return {
          success: false,
          message: `❌ Unknown command: ${args.command}. Use \`!help\` for available commands.`,
        };
    }
  },
});

// Send Discord notification action
export const sendDiscordNotification = action({
  args: {
    userId: v.id("users"),
    message: v.string(),
    embed: v.optional(v.object({
      title: v.optional(v.string()),
      description: v.optional(v.string()),
      color: v.optional(v.number()),
      fields: v.optional(v.array(v.object({
        name: v.string(),
        value: v.string(),
        inline: v.optional(v.boolean()),
      }))),
    })),
  },
  handler: async (ctx, args) => {
    // Get Discord user info
    const discordUser = await ctx.runQuery(internal.discord.getDiscordUser, {
      userId: args.userId,
    });

    if (!discordUser) {
      console.log(`No Discord account linked for user ${args.userId}`);
      return { success: false, reason: "no_discord_link" };
    }

    // Here we would integrate with Discord API to send the message
    // For now, we'll log it and return success
    console.log(`Discord notification for ${discordUser.discordId}:`, {
      message: args.message,
      embed: args.embed,
    });

    return { success: true, discordId: discordUser.discordId };
  },
});

// Broadcast announcement to Discord
export const broadcastAnnouncementToDiscord = action({
  args: {
    announcementId: v.id("announcements"),
    channelId: v.string(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.manage");

    const announcement = await ctx.runQuery(internal.announcements.getAnnouncement, {
      id: args.announcementId,
    });

    if (!announcement) {
      throw new Error("Announcement not found");
    }

    // Format announcement for Discord
    const embed = {
      title: `📢 ${announcement.title}`,
      description: announcement.content,
      color: announcement.type === "urgent" ? 0xff0000 : 
             announcement.type === "warning" ? 0xffa500 : 
             announcement.type === "celebration" ? 0x00ff00 : 0x0099ff,
      fields: [
        {
          name: "Type",
          value: announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1),
          inline: true,
        },
        {
          name: "Author",
          value: announcement.author?.username || "Unknown",
          inline: true,
        },
      ],
      timestamp: new Date(announcement.createdAt).toISOString(),
    };

    // Here we would send to Discord channel
    console.log(`Broadcasting announcement to Discord channel ${args.channelId}:`, embed);

    return { success: true, channelId: args.channelId };
  },
});

// Get Discord bot statistics
export const getDiscordStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, "admin.view");

    const totalLinked = await ctx.db
      .query("discordUsers")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const totalUsers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    return {
      totalUsers: totalUsers.length,
      linkedUsers: totalLinked.length,
      linkageRate: totalUsers.length > 0 ? Math.round((totalLinked.length / totalUsers.length) * 100) : 0,
    };
  },
});