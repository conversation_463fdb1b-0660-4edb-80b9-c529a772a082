import React from 'react';
import { Id } from '../../../convex/_generated/dataModel';

interface GuideCardProps {
  guide: {
    _id: Id<"guides">;
    title: string;
    content: string;
    category: string;
    isPublished: boolean;
    viewCount: number;
    tags: string[];
    createdAt: number;
    author: { username: string } | null;
  };
  onEdit?: (guide: any) => void;
  onDelete?: (guideId: Id<"guides">) => void;
  onView?: (guideId: Id<"guides">) => void;
  showActions?: boolean;
}

export const GuideCard: React.FC<GuideCardProps> = ({
  guide,
  onEdit,
  onDelete,
  onView,
  showActions = false,
}) => {
  const handleView = () => {
    if (onView) {
      onView(guide._id);
    }
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEdit) {
      onEdit(guide);
    }
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete && confirm('Are you sure you want to delete this guide?')) {
      onDelete(guide._id);
    }
  };

  const getContentPreview = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer"
      onClick={handleView}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">{guide.title}</h3>
          <div className="flex items-center gap-3 text-sm text-gray-600 mb-2">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
              {guide.category}
            </span>
            <span>{guide.author?.username || 'Unknown'}</span>
            <span>{guide.viewCount} views</span>
            <span>
              {new Date(guide.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
        {showActions && (
          <div className="flex gap-2">
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Edit
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:text-red-800 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        )}
      </div>

      <p className="text-gray-700 mb-4">
        {getContentPreview(guide.content)}
      </p>

      <div className="flex items-center justify-between">
        <div className="flex flex-wrap gap-1">
          {guide.tags.map((tag, index) => (
            <span
              key={index}
              className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
            >
              #{tag}
            </span>
          ))}
        </div>
        
        <div className="flex items-center gap-2">
          {!guide.isPublished && (
            <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
              Draft
            </span>
          )}
          <span className="text-blue-600 hover:text-blue-800 text-sm font-medium">
            Read More →
          </span>
        </div>
      </div>
    </div>
  );
};