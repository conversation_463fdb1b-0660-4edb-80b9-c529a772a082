import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requireAuth, getCurrentUser, requirePermission } from "./lib/permissions";

// Get all active announcements
export const getActiveAnnouncements = query({
  args: {},
  handler: async (ctx) => {
    const announcements = await ctx.db
      .query("announcements")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .filter((q) => {
        // Only show non-expired announcements
        const now = Date.now();
        return q.or(
          q.eq(q.field("expiresAt"), undefined),
          q.gt(q.field("expiresAt"), now)
        );
      })
      .order("desc")
      .collect();

    // Fetch author details for each announcement
    const announcementsWithAuthors = await Promise.all(
      announcements.map(async (announcement) => {
        const author = await ctx.db.get(announcement.authorId);
        return {
          ...announcement,
          author: author ? { 
            username: author.username, 
            avatar: author.avatar 
          } : null,
        };
      })
    );

    return announcementsWithAuthors;
  },
});

// Get pinned announcements
export const getPinnedAnnouncements = query({
  args: {},
  handler: async (ctx) => {
    const announcements = await ctx.db
      .query("announcements")
      .withIndex("by_pinned", (q) => q.eq("isPinned", true))
      .filter((q) => {
        const now = Date.now();
        return q.and(
          q.eq(q.field("isActive"), true),
          q.or(
            q.eq(q.field("expiresAt"), undefined),
            q.gt(q.field("expiresAt"), now)
          )
        );
      })
      .order("desc")
      .collect();

    // Fetch author details for each announcement
    const announcementsWithAuthors = await Promise.all(
      announcements.map(async (announcement) => {
        const author = await ctx.db.get(announcement.authorId);
        return {
          ...announcement,
          author: author ? { 
            username: author.username, 
            avatar: author.avatar 
          } : null,
        };
      })
    );

    return announcementsWithAuthors;
  },
});

// Get all announcements (admin only)
export const getAllAnnouncements = query({
  args: {
    limit: v.optional(v.number()),
    includeInactive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.manage");
    
    let query = ctx.db.query("announcements");
    
    if (!args.includeInactive) {
      query = ctx.db.query("announcements")
        .withIndex("by_active", (q: any) => q.eq("isActive", true));
    }
    
    const announcements = await query
      .order("desc")
      .take(args.limit ?? 50);

    // Fetch author details for each announcement
    const announcementsWithAuthors = await Promise.all(
      announcements.map(async (announcement) => {
        const author = await ctx.db.get(announcement.authorId);
        return {
          ...announcement,
          author: author ? { 
            username: author.username, 
            avatar: author.avatar 
          } : null,
        };
      })
    );

    return announcementsWithAuthors;
  },
});

// Get single announcement by ID
export const getAnnouncement = query({
  args: { id: v.id("announcements") },
  handler: async (ctx, args) => {
    const announcement = await ctx.db.get(args.id);
    if (!announcement) return null;

    const author = await ctx.db.get(announcement.authorId);
    return {
      ...announcement,
      author: author ? { 
        username: author.username, 
        avatar: author.avatar 
      } : null,
    };
  },
});

// Create announcement
export const createAnnouncement = mutation({
  args: {
    title: v.string(),
    content: v.string(),
    type: v.string(),
    isPinned: v.optional(v.boolean()),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.create");
    const user = await getCurrentUser(ctx);
    
    const now = Date.now();
    return await ctx.db.insert("announcements", {
      title: args.title,
      content: args.content,
      type: args.type,
      authorId: user._id,
      isActive: true,
      isPinned: args.isPinned ?? false,
      expiresAt: args.expiresAt,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update announcement
export const updateAnnouncement = mutation({
  args: {
    id: v.id("announcements"),
    title: v.optional(v.string()),
    content: v.optional(v.string()),
    type: v.optional(v.string()),
    isPinned: v.optional(v.boolean()),
    isActive: v.optional(v.boolean()),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.edit");
    
    const { id, ...updates } = args;
    return await ctx.db.patch(id, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Delete announcement (soft delete)
export const deleteAnnouncement = mutation({
  args: { id: v.id("announcements") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.delete");
    
    return await ctx.db.patch(args.id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// Permanently delete announcement
export const permanentlyDeleteAnnouncement = mutation({
  args: { id: v.id("announcements") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "admin.all");
    
    return await ctx.db.delete(args.id);
  },
});

// Toggle pin status
export const togglePin = mutation({
  args: { id: v.id("announcements") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "announcements.edit");
    
    const announcement = await ctx.db.get(args.id);
    if (!announcement) {
      throw new Error("Announcement not found");
    }
    
    return await ctx.db.patch(args.id, {
      isPinned: !announcement.isPinned,
      updatedAt: Date.now(),
    });
  },
});
