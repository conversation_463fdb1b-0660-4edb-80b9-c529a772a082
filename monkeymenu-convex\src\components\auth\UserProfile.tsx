import React, { useEffect, useState } from 'react';
import { useUser } from '@clerk/clerk-react';
import { TornApiClient, TornUser } from '../../lib/torn-api';

const apiKey = import.meta.env.VITE_TORN_API_KEY;
const tornClient = new TornApiClient(apiKey);

export function UserProfile() {
  const { user } = useUser();
  const [tornUser, setTornUser] = useState<TornUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTornUser() {
      if (!user?.publicMetadata?.tornId) return;
      setLoading(true);
      try {
        const data = await tornClient.getUserProfile(user.publicMetadata.tornId as number);
        setTornUser(data);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchTornUser();
  }, [user]);

  if (!user) return <div>Not signed in</div>;
  if (loading) return <div>Loading profile...</div>;
  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="max-w-xl mx-auto p-6 bg-slate-800 rounded-lg shadow-lg mt-8">
      <div className="flex items-center space-x-4">
        <img src={user.imageUrl} alt="avatar" className="w-16 h-16 rounded-full" />
        <div>
          <h2 className="text-2xl font-bold text-white">{user.username}</h2>
          <p className="text-slate-400">{user.primaryEmailAddress?.emailAddress}</p>
          {tornUser && (
            <>
              <p className="text-slate-300 mt-2">Torn: {tornUser.name} (ID: {tornUser.player_id})</p>
              <p className="text-slate-300">Level: {tornUser.level} | Faction: {tornUser.faction?.faction_name || 'None'}</p>
            </>
          )}
        </div>
      </div>
      {tornUser && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-white mb-2">Stats</h3>
          <div className="grid grid-cols-2 gap-4 text-slate-300">
            <div>Age: {tornUser.age}</div>
            <div>Karma: {tornUser.karma}</div>
            <div>Friends: {tornUser.friends}</div>
            <div>Enemies: {tornUser.enemies}</div>
            <div>Forum Posts: {tornUser.forum_posts}</div>
            <div>Donator: {tornUser.donator ? 'Yes' : 'No'}</div>
          </div>
        </div>
      )}
    </div>
  );
} 