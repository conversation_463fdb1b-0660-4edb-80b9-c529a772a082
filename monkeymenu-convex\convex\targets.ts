import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requireAuth, getCurrentUser, requirePermission } from "./lib/permissions";

// Get all active targets with filtering
export const getTargets = query({
  args: {
    levelMin: v.optional(v.number()),
    levelMax: v.optional(v.number()),
    respectMin: v.optional(v.number()),
    respectMax: v.optional(v.number()),
    fairFightMin: v.optional(v.number()),
    fairFightMax: v.optional(v.number()),
    status: v.optional(v.string()),
    faction: v.optional(v.string()),
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("targets")
      .withIndex("by_level")
      .filter((q) => q.eq(q.field("isActive"), true));

    // Apply level filters
    if (args.levelMin !== undefined) {
      query = query.filter((q) => q.gte(q.field("level"), args.levelMin!));
    }
    if (args.levelMax !== undefined) {
      query = query.filter((q) => q.lte(q.field("level"), args.levelMax!));
    }

    // Apply respect filters
    if (args.respectMin !== undefined) {
      query = query.filter((q) => 
        q.and(
          q.neq(q.field("respect"), undefined),
          q.gte(q.field("respect"), args.respectMin!)
        )
      );
    }
    if (args.respectMax !== undefined) {
      query = query.filter((q) => 
        q.and(
          q.neq(q.field("respect"), undefined),
          q.lte(q.field("respect"), args.respectMax!)
        )
      );
    }

    // Apply fair fight filters
    if (args.fairFightMin !== undefined) {
      query = query.filter((q) => 
        q.and(
          q.neq(q.field("fairFight"), undefined),
          q.gte(q.field("fairFight"), args.fairFightMin!)
        )
      );
    }
    if (args.fairFightMax !== undefined) {
      query = query.filter((q) => 
        q.and(
          q.neq(q.field("fairFight"), undefined),
          q.lte(q.field("fairFight"), args.fairFightMax!)
        )
      );
    }

    // Apply status filter
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }

    // Apply faction filter
    if (args.faction) {
      query = query.filter((q) => q.eq(q.field("faction"), args.faction));
    }

    let targets = await query.take(args.limit ?? 100);

    // Apply search filter (client-side for username search)
    if (args.search) {
      const searchTerm = args.search.toLowerCase();
      targets = targets.filter(target => 
        target.username.toLowerCase().includes(searchTerm) ||
        target.tornId.toString().includes(searchTerm)
      );
    }

    return targets;
  },
});

// Get target by Torn ID
export const getTargetByTornId = query({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("targets")
      .withIndex("by_torn_id", (q) => q.eq("tornId", args.tornId))
      .first();
  },
});

// Get recently updated targets
export const getRecentlyUpdatedTargets = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("targets")
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Get target statistics
export const getTargetStats = query({
  args: {},
  handler: async (ctx) => {
    const allTargets = await ctx.db
      .query("targets")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const stats = {
      total: allTargets.length,
      byStatus: {} as Record<string, number>,
      byLevel: {
        low: 0,      // 1-50
        medium: 0,   // 51-100
        high: 0,     // 101+
      },
      averageLevel: 0,
      averageRespect: 0,
      averageFairFight: 0,
    };

    let totalLevel = 0;
    let totalRespect = 0;
    let respectCount = 0;
    let totalFairFight = 0;
    let fairFightCount = 0;

    allTargets.forEach(target => {
      // Status counts
      stats.byStatus[target.status] = (stats.byStatus[target.status] || 0) + 1;

      // Level distribution
      if (target.level <= 50) stats.byLevel.low++;
      else if (target.level <= 100) stats.byLevel.medium++;
      else stats.byLevel.high++;

      // Averages
      totalLevel += target.level;
      
      if (target.respect !== undefined) {
        totalRespect += target.respect;
        respectCount++;
      }
      
      if (target.fairFight !== undefined) {
        totalFairFight += target.fairFight;
        fairFightCount++;
      }
    });

    stats.averageLevel = allTargets.length > 0 ? Math.round(totalLevel / allTargets.length) : 0;
    stats.averageRespect = respectCount > 0 ? Math.round(totalRespect / respectCount) : 0;
    stats.averageFairFight = fairFightCount > 0 ? Math.round((totalFairFight / fairFightCount) * 100) / 100 : 0;

    return stats;
  },
});

// Add or update target
export const upsertTarget = mutation({
  args: {
    tornId: v.number(),
    username: v.string(),
    level: v.number(),
    faction: v.optional(v.string()),
    status: v.string(),
    respect: v.optional(v.number()),
    fairFight: v.optional(v.number()),
    battleStats: v.optional(v.object({
      strength: v.number(),
      defense: v.number(),
      speed: v.number(),
      dexterity: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "targets.update");

    const existingTarget = await ctx.db
      .query("targets")
      .withIndex("by_torn_id", (q) => q.eq("tornId", args.tornId))
      .first();

    const now = Date.now();

    if (existingTarget) {
      // Update existing target
      return await ctx.db.patch(existingTarget._id, {
        username: args.username,
        level: args.level,
        faction: args.faction,
        status: args.status,
        respect: args.respect,
        fairFight: args.fairFight,
        battleStats: args.battleStats,
        lastUpdated: now,
        updatedAt: now,
      });
    } else {
      // Create new target
      return await ctx.db.insert("targets", {
        tornId: args.tornId,
        username: args.username,
        level: args.level,
        faction: args.faction,
        status: args.status,
        respect: args.respect,
        fairFight: args.fairFight,
        battleStats: args.battleStats,
        isActive: true,
        lastUpdated: now,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Batch update targets (for API imports)
export const batchUpdateTargets = mutation({
  args: {
    targets: v.array(v.object({
      tornId: v.number(),
      username: v.string(),
      level: v.number(),
      faction: v.optional(v.string()),
      status: v.string(),
      respect: v.optional(v.number()),
      fairFight: v.optional(v.number()),
      battleStats: v.optional(v.object({
        strength: v.number(),
        defense: v.number(),
        speed: v.number(),
        dexterity: v.number(),
      })),
    }))
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "targets.manage");

    const results = [];
    const now = Date.now();

    for (const targetData of args.targets) {
      const existingTarget = await ctx.db
        .query("targets")
        .withIndex("by_torn_id", (q) => q.eq("tornId", targetData.tornId))
        .first();

      if (existingTarget) {
        const result = await ctx.db.patch(existingTarget._id, {
          username: targetData.username,
          level: targetData.level,
          faction: targetData.faction,
          status: targetData.status,
          respect: targetData.respect,
          fairFight: targetData.fairFight,
          battleStats: targetData.battleStats,
          lastUpdated: now,
          updatedAt: now,
        });
        results.push({ action: 'updated', id: result });
      } else {
        const result = await ctx.db.insert("targets", {
          tornId: targetData.tornId,
          username: targetData.username,
          level: targetData.level,
          faction: targetData.faction,
          status: targetData.status,
          respect: targetData.respect,
          fairFight: targetData.fairFight,
          battleStats: targetData.battleStats,
          isActive: true,
          lastUpdated: now,
          createdAt: now,
          updatedAt: now,
        });
        results.push({ action: 'created', id: result });
      }
    }

    return { processed: results.length, results };
  },
});

// Remove target (soft delete)
export const removeTarget = mutation({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "targets.delete");

    const target = await ctx.db
      .query("targets")
      .withIndex("by_torn_id", (q) => q.eq("tornId", args.tornId))
      .first();

    if (!target) {
      throw new Error("Target not found");
    }

    return await ctx.db.patch(target._id, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// Permanently delete target
export const permanentlyDeleteTarget = mutation({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "admin.all");

    const target = await ctx.db
      .query("targets")
      .withIndex("by_torn_id", (q) => q.eq("tornId", args.tornId))
      .first();

    if (!target) {
      throw new Error("Target not found");
    }

    return await ctx.db.delete(target._id);
  },
});

// Get unique factions from targets
export const getTargetFactions = query({
  args: {},
  handler: async (ctx) => {
    const targets = await ctx.db
      .query("targets")
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    const factions = new Set<string>();
    targets.forEach(target => {
      if (target.faction) {
        factions.add(target.faction);
      }
    });

    return Array.from(factions).sort();
  },
});