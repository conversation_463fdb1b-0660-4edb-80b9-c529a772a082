import React, { useState, useCallback, useMemo } from 'react';
import { useUser } from '@clerk/clerk-react';
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { WithdrawalRequest } from './WithdrawalRequest';
import { TransactionHistory } from './TransactionHistory';
import { BankingStats } from './BankingStats';
import { AccountBalance } from './AccountBalance';
import { usePermissions } from '../../hooks/usePermissions';

type TabType = 'overview' | 'withdraw' | 'history' | 'admin';

export function BankingDashboard() {
  const { user } = useUser();
  const { canAccessBanking, canManageBanking } = usePermissions();
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const currentUser = useQuery(api.users.getCurrentUser);
  const cashBalance = useQuery(api.banking.getUserCashBalance, 
    currentUser ? { userId: currentUser._id } : "skip"
  );

  if (!canAccessBanking) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to access banking features.</p>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const tabs = useMemo(() => [
    { id: 'overview' as const, label: 'Overview', icon: '📊' },
    { id: 'withdraw' as const, label: 'Withdraw', icon: '💸' },
    { id: 'history' as const, label: 'History', icon: '📜' },
    ...(canManageBanking ? [{ id: 'admin' as const, label: 'Admin', icon: '⚙️' }] : [])
  ], [canManageBanking]);

  const handleTabChange = useCallback((tabId: TabType) => {
    setActiveTab(tabId);
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Banking Dashboard</h1>
        <p className="text-gray-600">Manage your cash and view transaction history</p>
      </div>

      {/* Account Balance Header */}
      <div className="mb-6">
        <AccountBalance balance={cashBalance ?? 0} />
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <BankingStats userId={currentUser._id} />
            <div className="grid md:grid-cols-2 gap-6">
              <WithdrawalRequest />
              <TransactionHistory userId={currentUser._id} limit={5} />
            </div>
          </div>
        )}

        {activeTab === 'withdraw' && (
          <WithdrawalRequest />
        )}

        {activeTab === 'history' && (
          <TransactionHistory userId={currentUser._id} />
        )}

        {activeTab === 'admin' && canManageBanking && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-yellow-800 mb-2">Admin Panel</h3>
            <p className="text-yellow-700">Admin banking features will be implemented here.</p>
          </div>
        )}
      </div>
    </div>
  );
}