import { createFileRoute } from '@tanstack/react-router';
import { ProtectedRoute } from '../components/auth/ProtectedRoute';
import { TargetsList } from '../components/targets/TargetsList';

export const Route = createFileRoute('/targets')({
  component: TargetsPage,
});

function TargetsPage() {
  return (
    <ProtectedRoute>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold text-gray-900">Target Finder</h1>
        <TargetsList />
      </div>
    </ProtectedRoute>
  );
}