import React from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface LeaderboardProps {
  category: 'respect' | 'attacks' | 'banking' | 'guides';
  title: string;
  limit?: number;
}

export function Leaderboard({ category, title, limit = 10 }: LeaderboardProps) {
  const leaderboard = useQuery(api.analytics.getLeaderboards, { category, limit });

  if (!leaderboard) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/2"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const getRankIcon = (position: number) => {
    switch (position) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${position}`;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'respect': return '⭐';
      case 'attacks': return '⚔️';
      case 'banking': return '💰';
      case 'guides': return '📚';
      default: return '🏆';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center gap-2 mb-4">
        <span className="text-xl">{getCategoryIcon(category)}</span>
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      </div>
      
      {leaderboard.data.length > 0 ? (
        <div className="space-y-3">
          {leaderboard.data.map((entry, index) => (
            <div
              key={entry.user._id}
              className={`flex items-center justify-between p-3 rounded-lg transition-colors ${
                index < 3 
                  ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200' 
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center gap-3">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                  index === 0 ? 'bg-yellow-100 text-yellow-800' :
                  index === 1 ? 'bg-gray-100 text-gray-700' :
                  index === 2 ? 'bg-orange-100 text-orange-700' :
                  'bg-blue-100 text-blue-700'
                }`}>
                  {typeof getRankIcon(index + 1) === 'string' && getRankIcon(index + 1).startsWith('#') 
                    ? getRankIcon(index + 1) 
                    : <span className="text-base">{getRankIcon(index + 1)}</span>
                  }
                </div>
                
                <div>
                  <div className="font-medium text-gray-900">{entry.user.username}</div>
                  <div className="text-xs text-gray-500">
                    {entry.user.faction && `${entry.user.faction} • `}
                    Level {entry.user.level || 'Unknown'}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-semibold text-gray-900">{entry.label}</div>
                <div className="text-xs text-gray-500">
                  {category === 'banking' && 'Current Balance'}
                  {category === 'respect' && 'Total Earned'}
                  {category === 'attacks' && 'Total Count'}
                  {category === 'guides' && 'Total Impact'}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <div className="text-4xl mb-3">🏆</div>
          <p className="text-gray-500">No data available yet</p>
          <p className="text-sm text-gray-400 mt-1">
            Start {category === 'guides' ? 'writing guides' : 'participating'} to appear on the leaderboard!
          </p>
        </div>
      )}
      
      {leaderboard.total > limit && (
        <div className="mt-4 pt-3 border-t border-gray-200 text-center">
          <p className="text-sm text-gray-500">
            Showing top {limit} of {leaderboard.total} {category === 'guides' ? 'authors' : 'users'}
          </p>
        </div>
      )}
    </div>
  );
}