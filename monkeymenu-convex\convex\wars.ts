import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requireAuth, getCurrentUser, requirePermission } from "./lib/permissions";

// Get active wars
export const getActiveWars = query({
  args: {},
  handler: async (ctx) => {
    return await ctx.db
      .query("wars")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .collect();
  },
});

// Get all wars with filtering
export const getWars = query({
  args: {
    status: v.optional(v.string()),
    factionId: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("wars")
      .filter((q: any) => q.eq(q.field("isActive"), true));

    if (args.status) {
      query = ctx.db.query("wars")
        .withIndex("by_status", (q: any) => q.eq("status", args.status))
        .filter((q: any) => q.eq(q.field("isActive"), true));
    }

    if (args.factionId) {
      query = ctx.db.query("wars")
        .withIndex("by_faction", (q: any) => q.eq("factionId", args.factionId))
        .filter((q: any) => q.eq(q.field("isActive"), true));
    }

    return await query
      .order("desc")
      .take(args.limit ?? 50);
  },
});

// Get war by ID with attacks
export const getWarWithAttacks = query({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    const war = await ctx.db.get(args.warId);
    if (!war) return null;

    const attacks = await ctx.db
      .query("warAttacks")
      .withIndex("by_war", (q) => q.eq("warId", args.warId))
      .order("desc")
      .collect();

    return { war, attacks };
  },
});

// Get war statistics
export const getWarStats = query({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    const war = await ctx.db.get(args.warId);
    if (!war) return null;

    const attacks = await ctx.db
      .query("warAttacks")
      .withIndex("by_war", (q) => q.eq("warId", args.warId))
      .collect();

    // Calculate statistics
    const totalAttacks = attacks.length;
    const wins = attacks.filter(a => a.result === 'win').length;
    const losses = attacks.filter(a => a.result === 'loss').length;
    const timeouts = attacks.filter(a => a.result === 'timeout').length;
    const escapes = attacks.filter(a => a.result === 'escape').length;

    const totalRespect = attacks.reduce((sum, a) => sum + a.respect, 0);
    const averageRespect = totalAttacks > 0 ? Math.round(totalRespect / totalAttacks) : 0;

    // Attacker performance
    const attackerStats = new Map<number, {
      name: string;
      attacks: number;
      wins: number;
      respect: number;
    }>();

    attacks.forEach(attack => {
      const existing = attackerStats.get(attack.attackerId) || {
        name: attack.attackerName,
        attacks: 0,
        wins: 0,
        respect: 0,
      };
      
      existing.attacks++;
      if (attack.result === 'win') existing.wins++;
      existing.respect += attack.respect;
      
      attackerStats.set(attack.attackerId, existing);
    });

    const topAttackers = Array.from(attackerStats.entries())
      .map(([id, stats]) => ({ id, ...stats }))
      .sort((a, b) => b.respect - a.respect)
      .slice(0, 10);

    return {
      war,
      totalAttacks,
      wins,
      losses,
      timeouts,
      escapes,
      winRate: totalAttacks > 0 ? Math.round((wins / totalAttacks) * 100) : 0,
      totalRespect,
      averageRespect,
      topAttackers,
    };
  },
});

// Get recent war activity
export const getRecentWarActivity = query({
  args: { limit: v.optional(v.number()) },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("warAttacks")
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Create new war
export const createWar = mutation({
  args: {
    factionId: v.number(),
    factionName: v.string(),
    enemyFactionId: v.number(),
    enemyFactionName: v.string(),
    startTime: v.number(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.create");

    const now = Date.now();
    return await ctx.db.insert("wars", {
      factionId: args.factionId,
      factionName: args.factionName,
      enemyFactionId: args.enemyFactionId,
      enemyFactionName: args.enemyFactionName,
      startTime: args.startTime,
      status: "active",
      ourScore: 0,
      enemyScore: 0,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update war status and scores
export const updateWar = mutation({
  args: {
    warId: v.id("wars"),
    status: v.optional(v.string()),
    ourScore: v.optional(v.number()),
    enemyScore: v.optional(v.number()),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.edit");

    const { warId, ...updates } = args;
    return await ctx.db.patch(warId, {
      ...updates,
      updatedAt: Date.now(),
    });
  },
});

// Add war attack
export const addWarAttack = mutation({
  args: {
    warId: v.id("wars"),
    attackerId: v.number(),
    attackerName: v.string(),
    defenderId: v.number(),
    defenderName: v.string(),
    result: v.string(),
    respect: v.number(),
    chain: v.optional(v.number()),
    timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.update");

    const now = Date.now();
    const attackId = await ctx.db.insert("warAttacks", {
      warId: args.warId,
      attackerId: args.attackerId,
      attackerName: args.attackerName,
      defenderId: args.defenderId,
      defenderName: args.defenderName,
      result: args.result,
      respect: args.respect,
      chain: args.chain,
      timestamp: args.timestamp,
      createdAt: now,
    });

    // Update war score if it's a win
    if (args.result === 'win') {
      const war = await ctx.db.get(args.warId);
      if (war) {
        await ctx.db.patch(args.warId, {
          ourScore: war.ourScore + 1,
          updatedAt: now,
        });
      }
    }

    return attackId;
  },
});

// Batch import war attacks
export const batchImportWarAttacks = mutation({
  args: {
    warId: v.id("wars"),
    attacks: v.array(v.object({
      attackerId: v.number(),
      attackerName: v.string(),
      defenderId: v.number(),
      defenderName: v.string(),
      result: v.string(),
      respect: v.number(),
      chain: v.optional(v.number()),
      timestamp: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.manage");

    const results = [];
    const now = Date.now();
    let ourWins = 0;

    for (const attack of args.attacks) {
      // Check if attack already exists
      const existing = await ctx.db
        .query("warAttacks")
        .withIndex("by_war", (q) => q.eq("warId", args.warId))
        .filter((q) => 
          q.and(
            q.eq(q.field("attackerId"), attack.attackerId),
            q.eq(q.field("defenderId"), attack.defenderId),
            q.eq(q.field("timestamp"), attack.timestamp)
          )
        )
        .first();

      if (!existing) {
        const attackId = await ctx.db.insert("warAttacks", {
          warId: args.warId,
          attackerId: attack.attackerId,
          attackerName: attack.attackerName,
          defenderId: attack.defenderId,
          defenderName: attack.defenderName,
          result: attack.result,
          respect: attack.respect,
          chain: attack.chain,
          timestamp: attack.timestamp,
          createdAt: now,
        });

        if (attack.result === 'win') {
          ourWins++;
        }

        results.push({ action: 'created', id: attackId });
      } else {
        results.push({ action: 'skipped', reason: 'duplicate' });
      }
    }

    // Update war score
    if (ourWins > 0) {
      const war = await ctx.db.get(args.warId);
      if (war) {
        await ctx.db.patch(args.warId, {
          ourScore: war.ourScore + ourWins,
          updatedAt: now,
        });
      }
    }

    return { processed: results.length, created: results.filter(r => r.action === 'created').length, results };
  },
});

// End war
export const endWar = mutation({
  args: { 
    warId: v.id("wars"),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.edit");

    return await ctx.db.patch(args.warId, {
      status: "ended",
      endTime: args.endTime || Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Delete war (soft delete)
export const deleteWar = mutation({
  args: { warId: v.id("wars") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, "wars.delete");

    return await ctx.db.patch(args.warId, {
      isActive: false,
      updatedAt: Date.now(),
    });
  },
});

// Get faction war history
export const getFactionWarHistory = query({
  args: { 
    factionId: v.number(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("wars")
      .withIndex("by_faction", (q) => q.eq("factionId", args.factionId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .order("desc")
      .take(args.limit ?? 20);
  },
});