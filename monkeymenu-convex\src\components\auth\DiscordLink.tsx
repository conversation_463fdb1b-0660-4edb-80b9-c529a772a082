import React, { useState } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

export function DiscordLink() {
  const [isLinking, setIsLinking] = useState(false);
  const [linkCode, setLinkCode] = useState('');
  const [showLinkForm, setShowLinkForm] = useState(false);

  const discordUser = useQuery(api.discord.getDiscordUser, {});
  const linkDiscordAccount = useMutation(api.discord.linkDiscordAccount);
  const unlinkDiscordAccount = useMutation(api.discord.unlinkDiscordAccount);

  const handleLink = async () => {
    if (!linkCode.trim()) return;

    setIsLinking(true);
    try {
      // In a real implementation, this would involve Discord OAuth
      // For now, we'll simulate with a manual link code
      const [discordId, username, discriminator] = linkCode.split('#');
      
      if (!discordId || !username) {
        throw new Error('Invalid format. Use: discordId#username#discriminator');
      }

      await linkDiscordAccount({
        discordId,
        username,
        discriminator: discriminator || '0000',
      });

      setShowLinkForm(false);
      setLinkCode('');
    } catch (error) {
      console.error('Failed to link Discord:', error);
      alert(`Failed to link Discord: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLinking(false);
    }
  };

  const handleUnlink = async () => {
    if (!confirm('Are you sure you want to unlink your Discord account?')) return;

    try {
      await unlinkDiscordAccount();
    } catch (error) {
      console.error('Failed to unlink Discord:', error);
      alert(`Failed to unlink Discord: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  if (discordUser) {
    return (
      <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold">
              {discordUser.avatar ? (
                <img 
                  src={`https://cdn.discordapp.com/avatars/${discordUser.discordId}/${discordUser.avatar}.png`}
                  alt={discordUser.username}
                  className="w-10 h-10 rounded-full"
                />
              ) : (
                discordUser.username.charAt(0).toUpperCase()
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                Discord Linked
              </h3>
              <p className="text-sm text-gray-600">
                {discordUser.username}#{discordUser.discriminator}
              </p>
            </div>
          </div>
          <button
            onClick={handleUnlink}
            className="px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
          >
            Unlink
          </button>
        </div>
        <div className="mt-3 text-xs text-gray-500">
          ✅ You can now use Discord bot commands and receive notifications
        </div>
      </div>
    );
  }

  if (showLinkForm) {
    return (
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-sm font-medium text-gray-900 mb-3">
          Link Discord Account
        </h3>
        <div className="space-y-3">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Discord Info (Format: discordId#username#discriminator)
            </label>
            <input
              type="text"
              value={linkCode}
              onChange={(e) => setLinkCode(e.target.value)}
              placeholder="*********#YourUsername#1234"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div className="text-xs text-gray-500">
            💡 To get your Discord ID: Enable Developer Mode in Discord settings, right-click your name, and select "Copy ID"
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleLink}
              disabled={isLinking || !linkCode.trim()}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLinking ? 'Linking...' : 'Link Account'}
            </button>
            <button
              onClick={() => setShowLinkForm(false)}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-4 p-4 bg-slate-700 rounded-lg text-white">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium">Link Discord Account</h3>
          <p className="text-xs text-gray-300 mt-1">
            Get notifications and use bot commands in Discord
          </p>
        </div>
        <button
          onClick={() => setShowLinkForm(true)}
          className="bg-indigo-600 hover:bg-indigo-700 px-3 py-1 rounded text-white text-sm transition-colors"
        >
          Link Discord
        </button>
      </div>
      <div className="mt-3 text-xs text-gray-400">
        🤖 Available commands: !balance, !withdraw, !targets, !war, !help
      </div>
    </div>
  );
} 