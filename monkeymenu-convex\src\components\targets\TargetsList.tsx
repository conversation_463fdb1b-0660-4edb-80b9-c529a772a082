import React, { useState, useMemo, useCallback } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { TargetCard } from './TargetCard';
import { TargetFilters } from './TargetFilters';
import { TargetStats } from './TargetStats';
import { usePermissions } from '../../hooks/usePermissions';

export function TargetsList() {
  const { canViewTargets } = usePermissions();
  const [filters, setFilters] = useState<{
    levelMin?: number;
    levelMax?: number;
    respectMin?: number;
    respectMax?: number;
    fairFightMin?: number;
    fairFightMax?: number;
    status?: string;
    faction?: string;
    search?: string;
  }>({});

  const [sortBy, setSortBy] = useState<'level' | 'respect' | 'fairFight' | 'lastUpdated'>('lastUpdated');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const targets = useQuery(
    canViewTargets() ? api.targets.getTargets : "skip",
    { ...filters, limit: 200 }
  );

  // Sort targets on client side
  const sortedTargets = useMemo(() => {
    if (!targets) return [];
    
    return [...targets].sort((a, b) => {
      let aValue: number;
      let bValue: number;
      
      switch (sortBy) {
        case 'level':
          aValue = a.level;
          bValue = b.level;
          break;
        case 'respect':
          aValue = a.respect ?? -1;
          bValue = b.respect ?? -1;
          break;
        case 'fairFight':
          aValue = a.fairFight ?? -1;
          bValue = b.fairFight ?? -1;
          break;
        case 'lastUpdated':
        default:
          aValue = a.lastUpdated;
          bValue = b.lastUpdated;
          break;
      }
      
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
  }, [targets, sortBy, sortOrder]);

  const handleSortChange = useCallback((newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  }, [sortBy, sortOrder]);

  const resetFilters = useCallback(() => {
    setFilters({});
  }, []);

  if (!canViewTargets()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Restricted</h2>
          <p className="text-gray-600">You don't have permission to view targets.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">🎯 Target Finder</h1>
        <p className="text-gray-600">Find and track targets for optimal attacks</p>
      </div>

      {/* Statistics */}
      <TargetStats />

      {/* Filters */}
      <TargetFilters 
        filters={filters}
        onFiltersChange={setFilters}
        onReset={resetFilters}
      />

      {/* Sort Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Sort by:</span>
            <div className="flex items-center space-x-2">
              {(['level', 'respect', 'fairFight', 'lastUpdated'] as const).map((option) => (
                <button
                  key={option}
                  onClick={() => handleSortChange(option)}
                  className={`px-3 py-1 text-sm rounded-full transition-colors ${
                    sortBy === option
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {option === 'lastUpdated' ? 'Last Updated' : 
                   option === 'fairFight' ? 'Fair Fight' :
                   option.charAt(0).toUpperCase() + option.slice(1)}
                  {sortBy === option && (
                    <span className="ml-1">
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            {sortedTargets.length} target{sortedTargets.length !== 1 ? 's' : ''} found
          </div>
        </div>
      </div>

      {/* Targets Grid */}
      {targets === undefined ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : sortedTargets.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedTargets.map((target) => (
            <TargetCard key={target._id} target={target} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">🎯</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No targets found
          </h3>
          <p className="text-gray-600 mb-4">
            {Object.keys(filters).length > 0 
              ? "Try adjusting your filters to find more targets."
              : "No targets have been added to the system yet."
            }
          </p>
          {Object.keys(filters).length > 0 && (
            <button
              onClick={resetFilters}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              Clear Filters
            </button>
          )}
        </div>
      )}
    </div>
  );
}