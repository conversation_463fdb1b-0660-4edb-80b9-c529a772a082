import React from 'react';
import { Id } from '../../../convex/_generated/dataModel';

interface TargetCardProps {
  target: {
    _id: Id<"targets">;
    tornId: number;
    username: string;
    level: number;
    faction?: string;
    status: string;
    respect?: number;
    fairFight?: number;
    battleStats?: {
      strength: number;
      defense: number;
      speed: number;
      dexterity: number;
    };
    lastUpdated: number;
  };
  onAttack?: (tornId: number) => void;
  showActions?: boolean;
}

const statusStyles = {
  active: 'bg-green-100 text-green-800',
  inactive: 'bg-gray-100 text-gray-800',
  hospitalized: 'bg-red-100 text-red-800',
  jailed: 'bg-orange-100 text-orange-800',
};

const statusIcons = {
  active: '🟢',
  inactive: '⚫',
  hospitalized: '🏥',
  jailed: '🔒',
};

export const TargetCard = React.memo(function TargetCard({ target, onAttack, showActions = true }: TargetCardProps) {
  const formatLastUpdated = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getTotalBattleStats = () => {
    if (!target.battleStats) return null;
    return target.battleStats.strength + target.battleStats.defense + 
           target.battleStats.speed + target.battleStats.dexterity;
  };

  const handleAttackClick = () => {
    if (onAttack) {
      onAttack(target.tornId);
    } else {
      // Open Torn attack page in new tab
      window.open(`https://www.torn.com/loader.php?sid=attack&user2ID=${target.tornId}`, '_blank');
    }
  };

  const totalStats = getTotalBattleStats();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className="flex flex-col">
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-gray-900">
                {target.username}
              </h3>
              <span className="text-sm text-gray-500">#{target.tornId}</span>
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <span className="text-sm text-gray-600">Level {target.level}</span>
              {target.faction && (
                <>
                  <span className="text-gray-400">•</span>
                  <span className="text-sm text-blue-600">{target.faction}</span>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full flex items-center space-x-1 ${
            statusStyles[target.status as keyof typeof statusStyles] || statusStyles.inactive
          }`}>
            <span>{statusIcons[target.status as keyof typeof statusIcons] || statusIcons.inactive}</span>
            <span className="capitalize">{target.status}</span>
          </span>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-3">
        {target.respect !== undefined && (
          <div className="text-sm">
            <span className="text-gray-600">Respect:</span>
            <span className="ml-1 font-semibold text-green-600">
              {target.respect.toLocaleString()}
            </span>
          </div>
        )}
        
        {target.fairFight !== undefined && (
          <div className="text-sm">
            <span className="text-gray-600">Fair Fight:</span>
            <span className="ml-1 font-semibold text-blue-600">
              {target.fairFight.toFixed(2)}
            </span>
          </div>
        )}
        
        {totalStats && (
          <div className="text-sm">
            <span className="text-gray-600">Total Stats:</span>
            <span className="ml-1 font-semibold text-purple-600">
              {totalStats.toLocaleString()}
            </span>
          </div>
        )}
      </div>

      {/* Battle Stats */}
      {target.battleStats && (
        <div className="mb-3">
          <div className="text-xs text-gray-600 mb-1">Battle Stats:</div>
          <div className="grid grid-cols-4 gap-2 text-xs">
            <div className="text-center">
              <div className="text-red-600 font-semibold">{target.battleStats.strength.toLocaleString()}</div>
              <div className="text-gray-500">STR</div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-semibold">{target.battleStats.defense.toLocaleString()}</div>
              <div className="text-gray-500">DEF</div>
            </div>
            <div className="text-center">
              <div className="text-green-600 font-semibold">{target.battleStats.speed.toLocaleString()}</div>
              <div className="text-gray-500">SPD</div>
            </div>
            <div className="text-center">
              <div className="text-purple-600 font-semibold">{target.battleStats.dexterity.toLocaleString()}</div>
              <div className="text-gray-500">DEX</div>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-3 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          Updated {formatLastUpdated(target.lastUpdated)}
        </div>
        
        {showActions && (
          <div className="flex items-center space-x-2">
            <button
              onClick={handleAttackClick}
              disabled={target.status === 'hospitalized' || target.status === 'jailed'}
              className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              ⚔️ Attack
            </button>
            <button
              onClick={() => window.open(`https://www.torn.com/profiles.php?XID=${target.tornId}`, '_blank')}
              className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
            >
              👤 Profile
            </button>
          </div>
        )}
      </div>
    </div>
  );
});