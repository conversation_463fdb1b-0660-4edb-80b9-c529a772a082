import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Internal helper function to get user by Clerk ID
export async function getUserByClerkIdInternal(ctx: any, clerkId: string) {
  return await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", clerkId))
    .first();
}

// Get user by Clerk ID (helper function)
export const getUserByClerkId = query({
  args: { clerkId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", args.clerkId))
      .first();
  },
});

// Get current user (without arguments, uses auth context)
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q) => q.eq("clerkId", identity.subject))
      .first();
  },
});

// Create or update user
export const createOrUpdateUser = mutation({
  args: {
    clerkId: v.string(),
    tornId: v.number(),
    username: v.string(),
    email: v.optional(v.string()),
    avatar: v.optional(v.string()),
    faction: v.optional(v.string()),
    level: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_id", (q: any) => q.eq("clerkId", args.clerkId))
      .first();

    const now = Date.now();

    if (existingUser) {
      // Update existing user
      return await ctx.db.patch(existingUser._id, {
        ...args,
        lastSeen: now,
        updatedAt: now,
      });
    } else {
      // Create new user
      return await ctx.db.insert("users", {
        ...args,
        permissions: [],
        isActive: true,
        lastSeen: now,
        createdAt: now,
        updatedAt: now,
      });
    }
  },
});

// Get user by Torn ID
export const getUserByTornId = query({
  args: { tornId: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_torn_id", (q: any) => q.eq("tornId", args.tornId))
      .first();
  },
});

// Update user permissions
export const updateUserPermissions = mutation({
  args: {
    userId: v.id("users"),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.userId, {
      permissions: args.permissions,
      updatedAt: Date.now(),
    });
  },
});
