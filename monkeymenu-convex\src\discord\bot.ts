import { Client, GatewayIntentBits, Message, EmbedBuilder } from 'discord.js';
import { ConvexHttpClient } from 'convex/browser';
import { api } from '../../convex/_generated/api';

const DISCORD_TOKEN = process.env.DISCORD_BOT_TOKEN;
const CONVEX_URL = process.env.CONVEX_URL || process.env.VITE_CONVEX_URL;
const COMMAND_PREFIX = process.env.DISCORD_COMMAND_PREFIX || '!';

if (!DISCORD_TOKEN) {
  console.error('DISCORD_BOT_TOKEN environment variable is required');
  process.exit(1);
}

if (!CONVEX_URL) {
  console.error('CONVEX_URL environment variable is required');
  process.exit(1);
}

class MonkeyMenuBot {
  private client: Client;
  private convex: ConvexHttpClient;

  constructor() {
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.DirectMessages,
      ],
    });

    this.convex = new ConvexHttpClient(CONVEX_URL);
    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    this.client.once('ready', () => {
      console.log(`✅ Bot logged in as ${this.client.user?.tag}!`);
      this.client.user?.setActivity('MonkeyMenu | !help', { type: 'WATCHING' as any });
    });

    this.client.on('messageCreate', async (message) => {
      if (message.author.bot) return;
      if (!message.content.startsWith(COMMAND_PREFIX)) return;

      await this.handleCommand(message);
    });

    this.client.on('error', (error) => {
      console.error('Discord client error:', error);
    });
  }

  private async handleCommand(message: Message) {
    const args = message.content.slice(COMMAND_PREFIX.length).trim().split(/ +/);
    const command = args.shift()?.toLowerCase();

    if (!command) return;

    try {
      // Show typing indicator
      await message.channel.sendTyping();

      // Handle bot commands through Convex
      const response = await this.convex.action(api.discord.handleBotCommand, {
        discordId: message.author.id,
        command,
        args,
        channelId: message.channel.id,
        guildId: message.guild?.id,
      });

      // Send response
      if (response.success) {
        if (response.embed) {
          const embed = new EmbedBuilder()
            .setTitle(response.embed.title || null)
            .setDescription(response.embed.description || null)
            .setColor(response.embed.color || 0x0099ff);

          if (response.embed.fields) {
            response.embed.fields.forEach(field => {
              embed.addFields({
                name: field.name,
                value: field.value,
                inline: field.inline || false,
              });
            });
          }

          if (response.embed.timestamp) {
            embed.setTimestamp(new Date(response.embed.timestamp));
          }

          if (response.embed.footer) {
            embed.setFooter({ text: response.embed.footer.text });
          }

          await message.reply({ embeds: [embed] });
        } else {
          await message.reply(response.message);
        }
      } else {
        await message.reply(response.message || '❌ Command failed');
      }
    } catch (error) {
      console.error('Error handling command:', error);
      await message.reply('❌ An error occurred while processing your command.');
    }
  }

  public async start() {
    try {
      await this.client.login(DISCORD_TOKEN);
    } catch (error) {
      console.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  public async stop() {
    await this.client.destroy();
  }
}

// Auto-start if this file is run directly
if (require.main === module) {
  const bot = new MonkeyMenuBot();
  
  bot.start().catch(console.error);

  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('Shutting down bot...');
    await bot.stop();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('Shutting down bot...');
    await bot.stop();
    process.exit(0);
  });
}

export default MonkeyMenuBot;