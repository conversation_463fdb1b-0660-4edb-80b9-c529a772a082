import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";

// Notification types
export const NotificationTypes = {
  WITHDRAWAL_REQUEST: "withdrawal_request",
  WITHDRAWAL_APPROVED: "withdrawal_approved",
  WITHDRAWAL_DECLINED: "withdrawal_declined",
  WITHDRAWAL_COMPLETED: "withdrawal_completed",
  NEW_ANNOUNCEMENT: "new_announcement",
  WAR_STARTED: "war_started",
  WAR_ENDED: "war_ended",
  TARGET_UPDATE: "target_update",
} as const;

// Send withdrawal notification
export const sendWithdrawalNotification = action({
  args: {
    type: v.string(),
    userId: v.id("users"),
    withdrawalRequestId: v.id("withdrawalRequests"),
    amount: v.number(),
    processedBy: v.optional(v.string()),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get withdrawal request details
    const request = await ctx.runQuery(internal.banking.getWithdrawalRequest, {
      id: args.withdrawalRequestId,
    });

    if (!request) return { success: false, reason: "request_not_found" };

    let embed;
    let message;

    switch (args.type) {
      case NotificationTypes.WITHDRAWAL_REQUEST:
        embed = {
          title: "💸 New Withdrawal Request",
          description: `A withdrawal request has been created`,
          color: 0x0099ff,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Status",
              value: "Pending Approval",
              inline: true,
            },
            {
              name: "Request ID",
              value: args.withdrawalRequestId,
              inline: false,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        message = `💸 Your withdrawal request for $${args.amount.toLocaleString()} has been created and is pending approval.`;
        break;

      case NotificationTypes.WITHDRAWAL_APPROVED:
        embed = {
          title: "✅ Withdrawal Request Approved",
          description: `Your withdrawal request has been approved`,
          color: 0x00ff00,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Approved By",
              value: args.processedBy || "Admin",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        message = `✅ Your withdrawal request for $${args.amount.toLocaleString()} has been approved by ${args.processedBy || "an admin"}.`;
        break;

      case NotificationTypes.WITHDRAWAL_DECLINED:
        embed = {
          title: "❌ Withdrawal Request Declined",
          description: `Your withdrawal request has been declined`,
          color: 0xff0000,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Declined By",
              value: args.processedBy || "Admin",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        
        if (args.reason) {
          embed.fields.push({
            name: "Reason",
            value: args.reason,
            inline: false,
          });
        }
        
        message = `❌ Your withdrawal request for $${args.amount.toLocaleString()} has been declined by ${args.processedBy || "an admin"}.`;
        if (args.reason) {
          message += `\nReason: ${args.reason}`;
        }
        break;

      case NotificationTypes.WITHDRAWAL_COMPLETED:
        embed = {
          title: "🎉 Withdrawal Completed",
          description: `Your withdrawal has been completed`,
          color: 0x00ff00,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Completed By",
              value: args.processedBy || "Admin",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        message = `🎉 Your withdrawal of $${args.amount.toLocaleString()} has been completed!`;
        break;

      default:
        return { success: false, reason: "unknown_notification_type" };
    }

    // Send notification to user
    return await ctx.runAction(internal.discord.sendDiscordNotification, {
      userId: args.userId,
      message,
      embed,
    });
  },
});

// Send announcement notification
export const sendAnnouncementNotification = action({
  args: {
    announcementId: v.id("announcements"),
    channelId: v.string(),
  },
  handler: async (ctx, args) => {
    const announcement = await ctx.runQuery(internal.announcements.getAnnouncement, {
      id: args.announcementId,
    });

    if (!announcement) {
      return { success: false, reason: "announcement_not_found" };
    }

    const typeEmojis: Record<string, string> = {
      info: "ℹ️",
      warning: "⚠️",
      urgent: "🚨",
      celebration: "🎉",
    };

    const typeColors: Record<string, number> = {
      info: 0x0099ff,
      warning: 0xffa500,
      urgent: 0xff0000,
      celebration: 0x00ff00,
    };

    const embed = {
      title: `${typeEmojis[announcement.type] || "📢"} ${announcement.title}`,
      description: announcement.content,
      color: typeColors[announcement.type] || 0x0099ff,
      fields: [
        {
          name: "Type",
          value: announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1),
          inline: true,
        },
        {
          name: "Author",
          value: announcement.author?.username || "Unknown",
          inline: true,
        },
      ],
      timestamp: new Date(announcement.createdAt).toISOString(),
    };

    if (announcement.isPinned) {
      embed.fields.push({
        name: "Status",
        value: "📌 Pinned",
        inline: true,
      });
    }

    // Here we would send to Discord channel using Discord API
    console.log(`Sending announcement notification to channel ${args.channelId}:`, embed);

    return { success: true, channelId: args.channelId };
  },
});

// Send war notification
export const sendWarNotification = action({
  args: {
    type: v.string(),
    warId: v.id("wars"),
    channelId: v.string(),
  },
  handler: async (ctx, args) => {
    const war = await ctx.runQuery(internal.wars.getWarWithAttacks, {
      warId: args.warId,
    });

    if (!war) {
      return { success: false, reason: "war_not_found" };
    }

    let embed;

    switch (args.type) {
      case NotificationTypes.WAR_STARTED:
        embed = {
          title: "⚔️ War Started!",
          description: `A new war has begun against ${war.war.enemyFactionName}`,
          color: 0xff0000,
          fields: [
            {
              name: "Enemy Faction",
              value: war.war.enemyFactionName,
              inline: true,
            },
            {
              name: "Started",
              value: new Date(war.war.startTime).toLocaleString(),
              inline: true,
            },
            {
              name: "Initial Score",
              value: `${war.war.ourScore} - ${war.war.enemyScore}`,
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        break;

      case NotificationTypes.WAR_ENDED:
        const result = war.war.ourScore > war.war.enemyScore ? "Victory! 🎉" : 
                      war.war.ourScore < war.war.enemyScore ? "Defeat 💀" : "Tie 🤝";
        
        const duration = war.war.endTime ? 
          Math.floor((war.war.endTime - war.war.startTime) / (1000 * 60)) : 0;
        const hours = Math.floor(duration / 60);
        const minutes = duration % 60;
        const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

        embed = {
          title: "🏁 War Ended!",
          description: `The war against ${war.war.enemyFactionName} has ended`,
          color: war.war.ourScore > war.war.enemyScore ? 0x00ff00 : 
                 war.war.ourScore < war.war.enemyScore ? 0xff0000 : 0xffa500,
          fields: [
            {
              name: "Result",
              value: result,
              inline: true,
            },
            {
              name: "Final Score",
              value: `${war.war.ourScore} - ${war.war.enemyScore}`,
              inline: true,
            },
            {
              name: "Duration",
              value: durationText,
              inline: true,
            },
            {
              name: "Total Attacks",
              value: war.attacks.length.toString(),
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        };
        break;

      default:
        return { success: false, reason: "unknown_war_notification_type" };
    }

    // Here we would send to Discord channel using Discord API
    console.log(`Sending war notification to channel ${args.channelId}:`, embed);

    return { success: true, channelId: args.channelId };
  },
});

// Send target update notification
export const sendTargetUpdateNotification = action({
  args: {
    targetsUpdated: v.number(),
    channelId: v.string(),
  },
  handler: async (ctx, args) => {
    const embed = {
      title: "🎯 Targets Updated",
      description: `Target database has been updated with fresh data`,
      color: 0x00ff00,
      fields: [
        {
          name: "Targets Updated",
          value: args.targetsUpdated.toString(),
          inline: true,
        },
        {
          name: "Last Update",
          value: new Date().toLocaleString(),
          inline: true,
        },
      ],
      timestamp: new Date().toISOString(),
    };

    // Here we would send to Discord channel using Discord API
    console.log(`Sending target update notification to channel ${args.channelId}:`, embed);

    return { success: true, channelId: args.channelId };
  },
});

// Bulk notify users (for admin announcements)
export const bulkNotifyUsers = action({
  args: {
    userIds: v.array(v.id("users")),
    message: v.string(),
    embed: v.optional(v.object({
      title: v.optional(v.string()),
      description: v.optional(v.string()),
      color: v.optional(v.number()),
      fields: v.optional(v.array(v.object({
        name: v.string(),
        value: v.string(),
        inline: v.optional(v.boolean()),
      }))),
    })),
  },
  handler: async (ctx, args) => {
    const results = [];

    for (const userId of args.userIds) {
      try {
        const result = await ctx.runAction(internal.discord.sendDiscordNotification, {
          userId,
          message: args.message,
          embed: args.embed,
        });
        results.push({ userId, success: result.success });
      } catch (error) {
        results.push({ userId, success: false, error: error instanceof Error ? error.message : "Unknown error" });
      }
    }

    const successful = results.filter(r => r.success).length;
    const failed = results.length - successful;

    return {
      success: true,
      total: results.length,
      successful,
      failed,
      results,
    };
  },
});