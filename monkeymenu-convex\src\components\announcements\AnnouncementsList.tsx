import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { AnnouncementCard } from './AnnouncementCard';
import { CreateAnnouncementDialog } from './CreateAnnouncementDialog';
import { usePermissions } from '../../hooks/usePermissions';

interface AnnouncementsListProps {
  showPinnedOnly?: boolean;
  showCreateButton?: boolean;
  limit?: number;
}

export function AnnouncementsList({ 
  showPinnedOnly = false, 
  showCreateButton = true,
  limit 
}: AnnouncementsListProps) {
  const { canCreateAnnouncements } = usePermissions();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<any>(null);

  const announcements = useQuery(
    showPinnedOnly 
      ? api.announcements.getPinnedAnnouncements 
      : api.announcements.getActiveAnnouncements
  );

  const displayedAnnouncements = limit && announcements 
    ? announcements.slice(0, limit) 
    : announcements;

  const handleEdit = (announcement: any) => {
    setEditingAnnouncement(announcement);
    setIsCreateDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false);
    setEditingAnnouncement(null);
  };

  if (announcements === undefined) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          {showPinnedOnly ? 'Pinned Announcements' : 'Announcements'}
          {displayedAnnouncements && (
            <span className="ml-2 text-sm text-gray-500">
              ({displayedAnnouncements.length})
            </span>
          )}
        </h2>
        
        {showCreateButton && canCreateAnnouncements() && (
          <button
            onClick={() => setIsCreateDialogOpen(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          >
            📢 Create Announcement
          </button>
        )}
      </div>

      {/* Announcements List */}
      {displayedAnnouncements && displayedAnnouncements.length > 0 ? (
        <div className="space-y-4">
          {displayedAnnouncements.map((announcement) => (
            <AnnouncementCard
              key={announcement._id}
              announcement={announcement}
              onEdit={handleEdit}
            />
          ))}
          
          {/* Show more link if limited */}
          {limit && announcements && announcements.length > limit && (
            <div className="text-center py-4">
              <button className="text-blue-600 hover:text-blue-800 font-medium">
                View {announcements.length - limit} more announcements →
              </button>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📢</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No announcements yet
          </h3>
          <p className="text-gray-600 mb-4">
            {showPinnedOnly 
              ? "No announcements are currently pinned." 
              : "There are no active announcements at the moment."
            }
          </p>
          {canCreateAnnouncements() && (
            <button
              onClick={() => setIsCreateDialogOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              Create First Announcement
            </button>
          )}
        </div>
      )}

      {/* Create/Edit Dialog */}
      <CreateAnnouncementDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        editingAnnouncement={editingAnnouncement}
      />
    </div>
  );
}