import React, { useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface GuideViewProps {
  guideId: Id<"guides">;
  onBack: () => void;
  onEdit?: (guide: any) => void;
  canEdit?: boolean;
}

export const GuideView: React.FC<GuideViewProps> = ({
  guideId,
  onBack,
  onEdit,
  canEdit = false,
}) => {
  const guide = useQuery(api.guides.getGuideById, { id: guideId });
  const incrementViewCount = useMutation(api.guides.incrementViewCount);

  useEffect(() => {
    if (guide) {
      incrementViewCount({ id: guideId });
    }
  }, [guide, guideId, incrementViewCount]);

  if (guide === undefined) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-1/3"></div>
        <div className="space-y-2 mb-6">
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (!guide) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Guide Not Found</h2>
        <p className="text-gray-600 mb-4">The guide you're looking for doesn't exist or has been removed.</p>
        <button
          onClick={onBack}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Back to Guides
        </button>
      </div>
    );
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(guide);
    }
  };

  const formatContent = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded">$1</code>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/\n/g, '<br>');
  };

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <button
            onClick={onBack}
            className="text-blue-600 hover:text-blue-800 flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Guides
          </button>
          {canEdit && (
            <button
              onClick={handleEdit}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Edit Guide
            </button>
          )}
        </div>

        <h1 className="text-3xl font-bold text-gray-900 mb-4">{guide.title}</h1>
        
        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
            {guide.category}
          </span>
          <span>By {guide.author?.username || 'Unknown'}</span>
          <span>{guide.viewCount} views</span>
          <span>
            {new Date(guide.createdAt).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric',
            })}
          </span>
          {guide.updatedAt !== guide.createdAt && (
            <span>
              Updated {new Date(guide.updatedAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </span>
          )}
          {!guide.isPublished && (
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full">
              Draft
            </span>
          )}
        </div>

        {guide.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {guide.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
              >
                #{tag}
              </span>
            ))}
          </div>
        )}
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        <div 
          className="prose prose-lg max-w-none"
          dangerouslySetInnerHTML={{ 
            __html: `<p>${formatContent(guide.content)}</p>` 
          }}
        />
      </div>
    </div>
  );
};