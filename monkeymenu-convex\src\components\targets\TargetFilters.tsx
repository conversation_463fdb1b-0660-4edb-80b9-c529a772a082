import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface TargetFiltersProps {
  filters: {
    levelMin?: number;
    levelMax?: number;
    respectMin?: number;
    respectMax?: number;
    fairFightMin?: number;
    fairFightMax?: number;
    status?: string;
    faction?: string;
    search?: string;
  };
  onFiltersChange: (filters: any) => void;
  onReset: () => void;
}

export function TargetFilters({ filters, onFiltersChange, onReset }: TargetFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const factions = useQuery(api.targets.getTargetFactions);

  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value === '' ? undefined : value,
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">🔍 Target Filters</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            {showAdvanced ? 'Less Filters' : 'More Filters'}
          </button>
          {hasActiveFilters && (
            <button
              onClick={onReset}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
            >
              Clear All
            </button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Search Username or ID
          </label>
          <input
            type="text"
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Enter username or Torn ID..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Basic Filters Row */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Level Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Min Level
            </label>
            <input
              type="number"
              value={filters.levelMin || ''}
              onChange={(e) => handleFilterChange('levelMin', e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="1"
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Max Level
            </label>
            <input
              type="number"
              value={filters.levelMax || ''}
              onChange={(e) => handleFilterChange('levelMax', e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="100"
              min="1"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="active">🟢 Active</option>
              <option value="inactive">⚫ Inactive</option>
              <option value="hospitalized">🏥 Hospitalized</option>
              <option value="jailed">🔒 Jailed</option>
            </select>
          </div>

          {/* Faction */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Faction
            </label>
            <select
              value={filters.faction || ''}
              onChange={(e) => handleFilterChange('faction', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Factions</option>
              {factions?.map((faction) => (
                <option key={faction} value={faction}>
                  {faction}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="border-t border-gray-200 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Respect Range */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Respect Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.respectMin || ''}
                    onChange={(e) => handleFilterChange('respectMin', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="Min respect"
                    min="0"
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="number"
                    value={filters.respectMax || ''}
                    onChange={(e) => handleFilterChange('respectMax', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="Max respect"
                    min="0"
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Fair Fight Range */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  Fair Fight Range
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    step="0.1"
                    value={filters.fairFightMin || ''}
                    onChange={(e) => handleFilterChange('fairFightMin', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="Min FF"
                    min="0"
                    max="3"
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <input
                    type="number"
                    step="0.1"
                    value={filters.fairFightMax || ''}
                    onChange={(e) => handleFilterChange('fairFightMax', e.target.value ? parseFloat(e.target.value) : undefined)}
                    placeholder="Max FF"
                    min="0"
                    max="3"
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Quick Filter Buttons */}
      <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-gray-200">
        <button
          onClick={() => onFiltersChange({ status: 'active' })}
          className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full hover:bg-green-200 transition-colors"
        >
          Active Only
        </button>
        <button
          onClick={() => onFiltersChange({ levelMin: 1, levelMax: 50 })}
          className="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
        >
          Low Level (1-50)
        </button>
        <button
          onClick={() => onFiltersChange({ fairFightMin: 1.5, fairFightMax: 3 })}
          className="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded-full hover:bg-purple-200 transition-colors"
        >
          High Fair Fight
        </button>
        <button
          onClick={() => onFiltersChange({ respectMin: 1000 })}
          className="px-3 py-1 text-sm bg-yellow-100 text-yellow-800 rounded-full hover:bg-yellow-200 transition-colors"
        >
          High Respect
        </button>
      </div>
    </div>
  );
}