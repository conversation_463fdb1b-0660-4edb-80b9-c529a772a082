import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

interface CreateAnnouncementDialogProps {
  isOpen: boolean;
  onClose: () => void;
  editingAnnouncement?: any;
}

export function CreateAnnouncementDialog({ 
  isOpen, 
  onClose, 
  editingAnnouncement 
}: CreateAnnouncementDialogProps) {
  const [title, setTitle] = useState(editingAnnouncement?.title || '');
  const [content, setContent] = useState(editingAnnouncement?.content || '');
  const [type, setType] = useState(editingAnnouncement?.type || 'info');
  const [isPinned, setIsPinned] = useState(editingAnnouncement?.isPinned || false);
  const [expiresAt, setExpiresAt] = useState(
    editingAnnouncement?.expiresAt 
      ? new Date(editingAnnouncement.expiresAt).toISOString().slice(0, 16) 
      : ''
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createAnnouncement = useMutation(api.announcements.createAnnouncement);
  const updateAnnouncement = useMutation(api.announcements.updateAnnouncement);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) return;

    setIsSubmitting(true);
    try {
      const expiresAtTimestamp = expiresAt ? new Date(expiresAt).getTime() : undefined;
      
      if (editingAnnouncement) {
        await updateAnnouncement({
          id: editingAnnouncement._id,
          title: title.trim(),
          content: content.trim(),
          type,
          isPinned,
          expiresAt: expiresAtTimestamp,
        });
      } else {
        await createAnnouncement({
          title: title.trim(),
          content: content.trim(),
          type,
          isPinned,
          expiresAt: expiresAtTimestamp,
        });
      }
      
      onClose();
      resetForm();
    } catch (error) {
      console.error('Failed to save announcement:', error);
      alert('Failed to save announcement');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setContent('');
    setType('info');
    setIsPinned(false);
    setExpiresAt('');
  };

  const handleClose = () => {
    onClose();
    if (!editingAnnouncement) {
      resetForm();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {editingAnnouncement ? 'Edit Announcement' : 'Create New Announcement'}
            </h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                Title *
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter announcement title"
                required
              />
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                Content *
              </label>
              <textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Enter announcement content"
                required
              />
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              {/* Type */}
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  id="type"
                  value={type}
                  onChange={(e) => setType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="info">📢 Info</option>
                  <option value="warning">⚠️ Warning</option>
                  <option value="urgent">🚨 Urgent</option>
                  <option value="celebration">🎉 Celebration</option>
                </select>
              </div>

              {/* Expires At */}
              <div>
                <label htmlFor="expiresAt" className="block text-sm font-medium text-gray-700 mb-1">
                  Expires At (Optional)
                </label>
                <input
                  type="datetime-local"
                  id="expiresAt"
                  value={expiresAt}
                  onChange={(e) => setExpiresAt(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Pin checkbox */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPinned"
                checked={isPinned}
                onChange={(e) => setIsPinned(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPinned" className="ml-2 block text-sm text-gray-700">
                📌 Pin this announcement
              </label>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !title.trim() || !content.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting 
                  ? (editingAnnouncement ? 'Updating...' : 'Creating...') 
                  : (editingAnnouncement ? 'Update' : 'Create')
                }
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}