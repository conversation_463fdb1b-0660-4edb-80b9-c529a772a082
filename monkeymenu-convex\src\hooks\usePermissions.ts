import { useSession } from './useSession';

export function usePermissions() {
  const { convexUser } = useSession();
  const permissions = convexUser?.permissions || [];

  function hasPermission(permission: string) {
    return permissions.includes(permission);
  }

  // Banking-specific permission helpers
  const canAccessBanking = () => {
    return hasPermission('banking.view') || 
           hasPermission('banking.manage') || 
           hasPermission('admin.all');
  };

  const canManageBanking = () => {
    return hasPermission('banking.manage') || 
           hasPermission('admin.all');
  };

  const isAdmin = () => {
    return hasPermission('admin.all');
  };

  // Announcements-specific permission helpers
  const canCreateAnnouncements = () => {
    return hasPermission('announcements.create') || 
           hasPermission('announcements.manage') || 
           hasPermission('admin.all');
  };

  const canEditAnnouncements = () => {
    return hasPermission('announcements.edit') || 
           hasPermission('announcements.manage') || 
           hasPermission('admin.all');
  };

  const canDeleteAnnouncements = () => {
    return hasPermission('announcements.delete') || 
           hasPermission('announcements.manage') || 
           hasPermission('admin.all');
  };

  const canManageAnnouncements = () => {
    return hasPermission('announcements.manage') || 
           hasPermission('admin.all');
  };

  // Targets-specific permission helpers
  const canViewTargets = () => {
    return hasPermission('targets.view') || 
           hasPermission('targets.manage') || 
           hasPermission('admin.all');
  };

  const canUpdateTargets = () => {
    return hasPermission('targets.update') || 
           hasPermission('targets.manage') || 
           hasPermission('admin.all');
  };

  const canDeleteTargets = () => {
    return hasPermission('targets.delete') || 
           hasPermission('targets.manage') || 
           hasPermission('admin.all');
  };

  const canManageTargets = () => {
    return hasPermission('targets.manage') || 
           hasPermission('admin.all');
  };

  // Wars-specific permission helpers
  const canViewWars = () => {
    return hasPermission('wars.view') || 
           hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  const canCreateWars = () => {
    return hasPermission('wars.create') || 
           hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  const canEditWars = () => {
    return hasPermission('wars.edit') || 
           hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  const canUpdateWars = () => {
    return hasPermission('wars.update') || 
           hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  const canDeleteWars = () => {
    return hasPermission('wars.delete') || 
           hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  const canManageWars = () => {
    return hasPermission('wars.manage') || 
           hasPermission('admin.all');
  };

  return {
    permissions,
    hasPermission,
    canAccessBanking,
    canManageBanking,
    canCreateAnnouncements,
    canEditAnnouncements,
    canDeleteAnnouncements,
    canManageAnnouncements,
    canViewTargets,
    canUpdateTargets,
    canDeleteTargets,
    canManageTargets,
    canViewWars,
    canCreateWars,
    canEditWars,
    canUpdateWars,
    canDeleteWars,
    canManageWars,
    isAdmin,
  };
} 