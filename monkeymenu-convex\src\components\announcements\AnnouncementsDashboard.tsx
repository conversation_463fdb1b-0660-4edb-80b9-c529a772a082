import React, { useState } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { AnnouncementCard } from './AnnouncementCard';
import { CreateAnnouncementDialog } from './CreateAnnouncementDialog';
import { usePermissions } from '../../hooks/usePermissions';

type TabType = 'active' | 'pinned' | 'manage';

export function AnnouncementsDashboard() {
  const { canCreateAnnouncements, canManageAnnouncements } = usePermissions();
  const [activeTab, setActiveTab] = useState<TabType>('active');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<any>(null);

  const activeAnnouncements = useQuery(api.announcements.getActiveAnnouncements);
  const pinnedAnnouncements = useQuery(api.announcements.getPinnedAnnouncements);
  const allAnnouncements = useQuery(
    canManageAnnouncements() 
      ? api.announcements.getAllAnnouncements 
      : "skip",
    { includeInactive: true, limit: 100 }
  );

  const handleEdit = (announcement: any) => {
    setEditingAnnouncement(announcement);
    setIsCreateDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false);
    setEditingAnnouncement(null);
  };

  const tabs = [
    { id: 'active' as const, label: 'Active', icon: '📢', count: activeAnnouncements?.length },
    { id: 'pinned' as const, label: 'Pinned', icon: '📌', count: pinnedAnnouncements?.length },
    ...(canManageAnnouncements() ? [
      { id: 'manage' as const, label: 'Manage', icon: '⚙️', count: allAnnouncements?.length }
    ] : [])
  ];

  const getCurrentAnnouncements = () => {
    switch (activeTab) {
      case 'active':
        return activeAnnouncements;
      case 'pinned':
        return pinnedAnnouncements;
      case 'manage':
        return allAnnouncements;
      default:
        return activeAnnouncements;
    }
  };

  const currentAnnouncements = getCurrentAnnouncements();

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Announcements</h1>
            <p className="text-gray-600">Stay updated with the latest faction news</p>
          </div>
          
          {canCreateAnnouncements() && (
            <button
              onClick={() => setIsCreateDialogOpen(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            >
              📢 New Announcement
            </button>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
              {tab.count !== undefined && (
                <span className={`px-2 py-0.5 text-xs rounded-full ${
                  activeTab === tab.id 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {currentAnnouncements === undefined ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : currentAnnouncements.length > 0 ? (
          <div className="space-y-4">
            {currentAnnouncements.map((announcement) => (
              <AnnouncementCard
                key={announcement._id}
                announcement={announcement}
                onEdit={handleEdit}
                showActions={canManageAnnouncements() || activeTab !== 'active'}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">
              {activeTab === 'pinned' ? '📌' : '📢'}
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {activeTab === 'pinned' 
                ? 'No pinned announcements'
                : activeTab === 'manage'
                ? 'No announcements to manage'
                : 'No active announcements'
              }
            </h3>
            <p className="text-gray-600 mb-4">
              {activeTab === 'pinned' 
                ? "No announcements are currently pinned."
                : activeTab === 'manage'
                ? "There are no announcements in the system yet."
                : "There are no active announcements at the moment."
              }
            </p>
            {canCreateAnnouncements() && (
              <button
                onClick={() => setIsCreateDialogOpen(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                Create First Announcement
              </button>
            )}
          </div>
        )}
      </div>

      {/* Create/Edit Dialog */}
      <CreateAnnouncementDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        editingAnnouncement={editingAnnouncement}
      />
    </div>
  );
}