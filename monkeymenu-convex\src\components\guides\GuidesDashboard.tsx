import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useSession } from '../../hooks/useSession';
import { GuidesList } from './GuidesList';
import { GuideView } from './GuideView';
import { CreateGuideDialog } from './CreateGuideDialog';
import { Id } from '../../../convex/_generated/dataModel';

type ViewMode = 'list' | 'view';

export const GuidesDashboard: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedGuideId, setSelectedGuideId] = useState<Id<"guides"> | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editGuide, setEditGuide] = useState<any>(null);
  const [showMyGuides, setShowMyGuides] = useState(false);

  const { user } = useSession();
  const deleteGuide = useMutation(api.guides.deleteGuide);
  const userGuides = useQuery(api.guides.getUserGuides, showMyGuides ? {} : 'skip');

  const handleViewGuide = (guideId: Id<"guides">) => {
    setSelectedGuideId(guideId);
    setViewMode('view');
  };

  const handleBackToList = () => {
    setViewMode('list');
    setSelectedGuideId(null);
  };

  const handleCreateGuide = () => {
    setEditGuide(null);
    setIsCreateDialogOpen(true);
  };

  const handleEditGuide = (guide: any) => {
    setEditGuide(guide);
    setIsCreateDialogOpen(true);
  };

  const handleDeleteGuide = async (guideId: Id<"guides">) => {
    try {
      await deleteGuide({ id: guideId });
    } catch (error) {
      console.error('Error deleting guide:', error);
      alert('Failed to delete guide. Please try again.');
    }
  };

  const handleCloseDialog = () => {
    setIsCreateDialogOpen(false);
    setEditGuide(null);
  };

  const canCreateGuides = user?.permissions.includes('admin') || user?.permissions.includes('guides:create');
  const canManageGuides = user?.permissions.includes('admin') || user?.permissions.includes('guides:manage');

  if (viewMode === 'view' && selectedGuideId) {
    return (
      <GuideView
        guideId={selectedGuideId}
        onBack={handleBackToList}
        onEdit={handleEditGuide}
        canEdit={canManageGuides}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Guides</h1>
          <p className="text-gray-600 mt-1">
            Browse helpful guides and tutorials created by the community
          </p>
        </div>
        
        <div className="flex gap-3">
          {canCreateGuides && (
            <>
              <button
                onClick={() => setShowMyGuides(!showMyGuides)}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  showMyGuides
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {showMyGuides ? 'Show All Guides' : 'My Guides'}
              </button>
              <button
                onClick={handleCreateGuide}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Guide
              </button>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Guides</p>
              <p className="text-2xl font-semibold text-gray-900">
                {showMyGuides ? (userGuides?.length || 0) : '∞'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Categories</p>
              <p className="text-2xl font-semibold text-gray-900">∞</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Popular Tags</p>
              <p className="text-2xl font-semibold text-gray-900">∞</p>
            </div>
          </div>
        </div>
      </div>

      {/* Guides List */}
      {showMyGuides && userGuides ? (
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-gray-900">My Guides</h2>
          {userGuides.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {userGuides.map((guide) => (
                <div key={guide._id} className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{guide.title}</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEditGuide(guide)}
                        className="text-blue-600 hover:text-blue-800 text-sm"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDeleteGuide(guide._id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-sm text-gray-600 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {guide.category}
                    </span>
                    <span>{guide.viewCount} views</span>
                    {!guide.isPublished && (
                      <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                        Draft
                      </span>
                    )}
                  </div>
                  <button
                    onClick={() => handleViewGuide(guide._id)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View Guide →
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600 mb-4">You haven't created any guides yet.</p>
              <button
                onClick={handleCreateGuide}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create Your First Guide
              </button>
            </div>
          )}
        </div>
      ) : (
        <GuidesList
          publishedOnly={!canManageGuides}
          onView={handleViewGuide}
          onEdit={canManageGuides ? handleEditGuide : undefined}
          onDelete={canManageGuides ? handleDeleteGuide : undefined}
          showActions={canManageGuides}
        />
      )}

      {/* Create/Edit Dialog */}
      <CreateGuideDialog
        isOpen={isCreateDialogOpen}
        onClose={handleCloseDialog}
        editGuide={editGuide}
      />
    </div>
  );
};