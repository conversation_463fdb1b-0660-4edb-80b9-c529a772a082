import { createRootRoute, Outlet, <PERSON> } from '@tanstack/react-router';
import { useUser } from '@clerk/clerk-react';
import { SignedIn, SignedOut, UserButton } from '@clerk/clerk-react';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';
import { useSession } from '../hooks/useSession';

export const Route = createRootRoute({
  component: RootComponent,
});

function RootComponent() {
  const { user } = useSession();
  const isAdmin = user?.permissions.includes('admin');

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="text-xl font-bold text-gray-900">
                MonkeyMenu
              </Link>
              <SignedIn>
                <div className="ml-8 flex space-x-4">
                  <Link
                    to="/dashboard"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Dashboard
                  </Link>
                  <Link
                    to="/banking"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Banking
                  </Link>
                  <Link
                    to="/announcements"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Announcements
                  </Link>
                  <Link
                    to="/targets"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Targets
                  </Link>
                  <Link
                    to="/wars"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Wars
                  </Link>
                  <Link
                    to="/guides"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Guides
                  </Link>
                  <Link
                    to="/analytics"
                    className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                    activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                  >
                    Analytics
                  </Link>
                  {isAdmin && (
                    <Link
                      to="/admin"
                      className="text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
                      activeProps={{ className: 'text-blue-600 bg-blue-50' }}
                    >
                      Admin
                    </Link>
                  )}
                </div>
              </SignedIn>
            </div>
            <div className="flex items-center">
              <SignedIn>
                <UserButton />
              </SignedIn>
              <SignedOut>
                <Link
                  to="/sign-in"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Sign In
                </Link>
              </SignedOut>
            </div>
          </div>
        </div>
      </nav>
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <Outlet />
      </main>
      <TanStackRouterDevtools />
    </div>
  );
}