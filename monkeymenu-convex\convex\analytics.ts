import { query } from "./_generated/server";
import { v } from "convex/values";
import { getUserByClerkIdInternal } from "./users";

export const getUserStats = query({
  args: {
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const timeRange = args.timeRange || "30d";
    const days = parseInt(timeRange);
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    // Get banking stats
    const userAccounts = await ctx.db
      .query("accounts")
      .filter((q) => q.eq(q.field("userId"), user._id))
      .collect();

    const userTransactions = await ctx.db
      .query("transactions")
      .filter((q) => 
        q.or(
          q.eq(q.field("fromAccountId"), userAccounts[0]?._id),
          q.eq(q.field("toAccountId"), userAccounts[0]?._id)
        )
      )
      .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
      .collect();

    // Get war participation
    const userWarAttacks = await ctx.db
      .query("warAttacks")
      .filter((q) => q.eq(q.field("attackerId"), user.tornId))
      .filter((q) => q.gte(q.field("timestamp"), cutoffTime))
      .collect();

    // Get guide stats if user created any
    const userGuides = await ctx.db
      .query("guides")
      .filter((q) => q.eq(q.field("authorId"), user._id))
      .collect();

    const totalViews = userGuides.reduce((sum, guide) => sum + guide.viewCount, 0);

    return {
      banking: {
        totalTransactions: userTransactions.length,
        totalDeposits: userTransactions
          .filter(t => t.type === "deposit")
          .reduce((sum, t) => sum + t.amount, 0),
        totalWithdrawals: userTransactions
          .filter(t => t.type === "withdrawal")
          .reduce((sum, t) => sum + t.amount, 0),
        currentBalance: userAccounts[0]?.balance || 0,
      },
      wars: {
        totalAttacks: userWarAttacks.length,
        wins: userWarAttacks.filter(a => a.result === "win").length,
        losses: userWarAttacks.filter(a => a.result === "loss").length,
        totalRespect: userWarAttacks.reduce((sum, a) => sum + a.respect, 0),
        winRate: userWarAttacks.length > 0 
          ? Math.round((userWarAttacks.filter(a => a.result === "win").length / userWarAttacks.length) * 100)
          : 0,
      },
      guides: {
        totalGuides: userGuides.length,
        publishedGuides: userGuides.filter(g => g.isPublished).length,
        totalViews,
        averageViews: userGuides.length > 0 ? Math.round(totalViews / userGuides.length) : 0,
      },
      activity: {
        timeRange,
        lastSeen: user.lastSeen,
        memberSince: user.createdAt,
      },
    };
  },
});

export const getFactionStats = query({
  args: {
    timeRange: v.optional(v.union(
      v.literal("7d"),
      v.literal("30d"),
      v.literal("90d"),
      v.literal("1y")
    )),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const timeRange = args.timeRange || "30d";
    const days = parseInt(timeRange);
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    // Get all faction members
    const factionMembers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("faction"), user.faction))
      .collect();

    // Get total banking activity
    const allTransactions = await ctx.db
      .query("transactions")
      .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
      .collect();

    // Get war statistics
    const activeWars = await ctx.db
      .query("wars")
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    const recentWarAttacks = await ctx.db
      .query("warAttacks")
      .filter((q) => q.gte(q.field("timestamp"), cutoffTime))
      .collect();

    // Get announcement engagement
    const recentAnnouncements = await ctx.db
      .query("announcements")
      .filter((q) => q.eq(q.field("isActive"), true))
      .filter((q) => q.gte(q.field("createdAt"), cutoffTime))
      .collect();

    return {
      members: {
        total: factionMembers.length,
        active: factionMembers.filter(m => 
          m.isActive && (Date.now() - m.lastSeen) < (7 * 24 * 60 * 60 * 1000)
        ).length,
        newMembers: factionMembers.filter(m => m.createdAt >= cutoffTime).length,
      },
      banking: {
        totalTransactions: allTransactions.length,
        totalVolume: allTransactions.reduce((sum, t) => sum + t.amount, 0),
        activeUsers: new Set(allTransactions.map(t => t.fromAccountId)).size,
      },
      wars: {
        activeWars: activeWars.length,
        totalAttacks: recentWarAttacks.length,
        totalRespect: recentWarAttacks.reduce((sum, a) => sum + a.respect, 0),
        winRate: recentWarAttacks.length > 0
          ? Math.round((recentWarAttacks.filter(a => a.result === "win").length / recentWarAttacks.length) * 100)
          : 0,
      },
      engagement: {
        announcements: recentAnnouncements.length,
        timeRange,
      },
    };
  },
});

export const getActivityTrends = query({
  args: {
    metric: v.union(
      v.literal("users"),
      v.literal("transactions"),
      v.literal("wars"),
      v.literal("guides")
    ),
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const days = args.days || 30;
    const now = Date.now();
    const trends = [];

    // Generate daily data points
    for (let i = days - 1; i >= 0; i--) {
      const dayStart = now - (i * 24 * 60 * 60 * 1000);
      const dayEnd = dayStart + (24 * 60 * 60 * 1000);

      let value = 0;

      switch (args.metric) {
        case "users":
          const dayUsers = await ctx.db
            .query("users")
            .filter((q) => q.gte(q.field("createdAt"), dayStart))
            .filter((q) => q.lt(q.field("createdAt"), dayEnd))
            .collect();
          value = dayUsers.length;
          break;

        case "transactions":
          const dayTransactions = await ctx.db
            .query("transactions")
            .filter((q) => q.gte(q.field("createdAt"), dayStart))
            .filter((q) => q.lt(q.field("createdAt"), dayEnd))
            .collect();
          value = dayTransactions.length;
          break;

        case "wars":
          const dayWarAttacks = await ctx.db
            .query("warAttacks")
            .filter((q) => q.gte(q.field("timestamp"), dayStart))
            .filter((q) => q.lt(q.field("timestamp"), dayEnd))
            .collect();
          value = dayWarAttacks.length;
          break;

        case "guides":
          const dayGuides = await ctx.db
            .query("guides")
            .filter((q) => q.gte(q.field("createdAt"), dayStart))
            .filter((q) => q.lt(q.field("createdAt"), dayEnd))
            .collect();
          value = dayGuides.length;
          break;
      }

      trends.push({
        date: new Date(dayStart).toISOString().split('T')[0],
        value,
        timestamp: dayStart,
      });
    }

    return {
      metric: args.metric,
      data: trends,
      totalDays: days,
      total: trends.reduce((sum, item) => sum + item.value, 0),
      average: Math.round(trends.reduce((sum, item) => sum + item.value, 0) / days * 100) / 100,
    };
  },
});

export const getLeaderboards = query({
  args: {
    category: v.union(
      v.literal("respect"),
      v.literal("attacks"),
      v.literal("banking"),
      v.literal("guides")
    ),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await getUserByClerkIdInternal(ctx, identity.subject);
    if (!user) {
      throw new Error("User not found");
    }

    const limit = args.limit || 10;
    const results = [];

    switch (args.category) {
      case "respect":
        const warAttacks = await ctx.db.query("warAttacks").collect();
        const respectByUser = warAttacks.reduce((acc, attack) => {
          acc[attack.attackerId] = (acc[attack.attackerId] || 0) + attack.respect;
          return acc;
        }, {} as Record<number, number>);

        const users = await ctx.db.query("users").collect();
        for (const [tornId, respect] of Object.entries(respectByUser)) {
          const attackerUser = users.find(u => u.tornId === parseInt(tornId));
          if (attackerUser) {
            results.push({
              user: attackerUser,
              value: respect,
              label: `${respect.toLocaleString()} respect`,
            });
          }
        }
        results.sort((a, b) => b.value - a.value);
        break;

      case "attacks":
        const attacksByUser = warAttacks.reduce((acc, attack) => {
          acc[attack.attackerId] = (acc[attack.attackerId] || 0) + 1;
          return acc;
        }, {} as Record<number, number>);

        for (const [tornId, attacks] of Object.entries(attacksByUser)) {
          const attackerUser = users.find(u => u.tornId === parseInt(tornId));
          if (attackerUser) {
            results.push({
              user: attackerUser,
              value: attacks,
              label: `${attacks} attacks`,
            });
          }
        }
        results.sort((a, b) => b.value - a.value);
        break;

      case "banking":
        const transactions = await ctx.db.query("transactions").collect();
        const accounts = await ctx.db.query("accounts").collect();
        
        const balanceByUser = accounts.reduce((acc, account) => {
          const accountUser = users.find(u => u._id === account.userId);
          if (accountUser) {
            acc[accountUser.tornId] = account.balance;
          }
          return acc;
        }, {} as Record<number, number>);

        for (const [tornId, balance] of Object.entries(balanceByUser)) {
          const accountUser = users.find(u => u.tornId === parseInt(tornId));
          if (accountUser) {
            results.push({
              user: accountUser,
              value: balance,
              label: `$${balance.toLocaleString()}`,
            });
          }
        }
        results.sort((a, b) => b.value - a.value);
        break;

      case "guides":
        const guides = await ctx.db.query("guides").collect();
        const guidesByAuthor = guides.reduce((acc, guide) => {
          const authorId = guide.authorId;
          if (!acc[authorId]) {
            acc[authorId] = { count: 0, views: 0 };
          }
          acc[authorId].count++;
          acc[authorId].views += guide.viewCount;
          return acc;
        }, {} as Record<string, { count: number; views: number }>);

        for (const [authorId, stats] of Object.entries(guidesByAuthor)) {
          const author = await ctx.db.get(authorId as any);
          if (author) {
            results.push({
              user: author,
              value: stats.views,
              label: `${stats.count} guides, ${stats.views.toLocaleString()} views`,
            });
          }
        }
        results.sort((a, b) => b.value - a.value);
        break;
    }

    return {
      category: args.category,
      data: results.slice(0, limit),
      total: results.length,
    };
  },
});