import React, { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";

interface BankingStatsProps {
  userId: Id<"users">;
}

export function BankingStats({ userId }: BankingStatsProps) {
  const [timeframe, setTimeframe] = useState(30);
  
  const stats = useQuery(api.banking.getBankingStats, {
    userId,
    days: timeframe
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  const timeframeOptions = [
    { value: 7, label: '7 days' },
    { value: 30, label: '30 days' },
    { value: 90, label: '90 days' },
    { value: 365, label: '1 year' }
  ];

  if (!stats) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Transactions',
      value: stats.totalTransactions,
      icon: '📊',
      color: 'blue'
    },
    {
      title: 'Total Deposits',
      value: formatCurrency(stats.depositAmount),
      subtitle: `${stats.totalDeposits} transactions`,
      icon: '📥',
      color: 'green'
    },
    {
      title: 'Total Withdrawals',
      value: formatCurrency(stats.withdrawalAmount),
      subtitle: `${stats.totalWithdrawals} transactions`,
      icon: '📤',
      color: 'red'
    },
    {
      title: 'Total Transfers',
      value: formatCurrency(stats.transferAmount),
      subtitle: `${stats.totalTransfers} transactions`,
      icon: '🔄',
      color: 'purple'
    }
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-700 border-blue-200',
      green: 'bg-green-50 text-green-700 border-green-200', 
      red: 'bg-red-50 text-red-700 border-red-200',
      purple: 'bg-purple-50 text-purple-700 border-purple-200'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-900">Banking Statistics</h3>
        <select
          value={timeframe}
          onChange={(e) => setTimeframe(Number(e.target.value))}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
        >
          {timeframeOptions.map(option => (
            <option key={option.value} value={option.value}>
              Last {option.label}
            </option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {statCards.map((card, index) => (
          <div key={index} className={`rounded-lg border p-4 ${getColorClasses(card.color)}`}>
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium opacity-80">{card.title}</p>
              <span className="text-lg opacity-70">{card.icon}</span>
            </div>
            <p className="text-2xl font-bold mb-1">{card.value}</p>
            {card.subtitle && (
              <p className="text-xs opacity-70">{card.subtitle}</p>
            )}
          </div>
        ))}
      </div>

      {/* Withdrawal Request Summary */}
      <div className="border-t pt-4">
        <h4 className="text-lg font-medium mb-3 text-gray-900">Withdrawal Requests</h4>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-yellow-600">{stats.pendingWithdrawals}</p>
            <p className="text-sm text-gray-600">Pending</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{stats.completedWithdrawals}</p>
            <p className="text-sm text-gray-600">Completed</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-600">{stats.cancelledWithdrawals}</p>
            <p className="text-sm text-gray-600">Cancelled</p>
          </div>
        </div>
      </div>
    </div>
  );
}