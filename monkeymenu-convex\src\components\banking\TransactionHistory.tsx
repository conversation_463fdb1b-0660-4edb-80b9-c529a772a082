import React, { useState } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";

interface TransactionHistoryProps {
  userId: Id<"users">;
  limit?: number;
  showFilters?: boolean;
}

export function TransactionHistory({ userId, limit, showFilters = true }: TransactionHistoryProps) {
  const [filters, setFilters] = useState({
    type: '',
    status: '',
    currency: 'cash'
  });
  const [currentPage, setCurrentPage] = useState(0);
  const pageSize = limit || 20;

  const transactions = useQuery(api.banking.getTransactionHistory, {
    userId,
    currency: filters.currency || undefined,
    type: filters.type || undefined,
    status: filters.status || undefined,
    limit: pageSize,
    offset: currentPage * pageSize
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit': return '📥';
      case 'withdrawal': return '📤';
      case 'transfer': return '🔄';
      case 'fee': return '💳';
      case 'withdrawal_cancelled': return '❌';
      default: return '💰';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const isIncoming = (transaction: any) => {
    return transaction.toUser && transaction.toUser._id === userId;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-6 text-gray-900">Transaction History</h3>
      
      {showFilters && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type-filter"
              value={filters.type}
              onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="deposit">Deposit</option>
              <option value="withdrawal">Withdrawal</option>
              <option value="transfer">Transfer</option>
              <option value="fee">Fee</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="currency-filter" className="block text-sm font-medium text-gray-700 mb-1">
              Currency
            </label>
            <select
              id="currency-filter"
              value={filters.currency}
              onChange={(e) => setFilters(prev => ({ ...prev, currency: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="cash">Cash</option>
              <option value="points">Points</option>
              <option value="tokens">Tokens</option>
            </select>
          </div>
        </div>
      )}

      {!transactions ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : transactions.transactions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No transactions found.</p>
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {transactions.transactions.map((transaction) => (
              <div key={transaction._id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900 capitalize">
                        {transaction.type.replace('_', ' ')}
                      </p>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(transaction.status)}`}>
                        {transaction.status}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500">
                      {new Date(transaction.createdAt).toLocaleString()}
                    </p>
                    {transaction.reference && (
                      <p className="text-xs text-gray-400">Ref: {transaction.reference}</p>
                    )}
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`font-semibold ${
                    isIncoming(transaction) ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {isIncoming(transaction) ? '+' : '-'}{formatCurrency(transaction.amount)}
                  </p>
                  <p className="text-xs text-gray-500 uppercase">
                    {transaction.currency}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {transactions.hasMore && (
            <div className="flex justify-center mt-6">
              <button
                onClick={() => setCurrentPage(prev => prev + 1)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Load More
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
}