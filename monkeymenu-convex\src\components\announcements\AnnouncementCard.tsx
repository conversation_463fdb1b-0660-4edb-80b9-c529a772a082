import React from 'react';
import { usePermissions } from '../../hooks/usePermissions';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Id } from '../../../convex/_generated/dataModel';

interface AnnouncementCardProps {
  announcement: {
    _id: Id<"announcements">;
    title: string;
    content: string;
    type: string;
    isPinned: boolean;
    isActive: boolean;
    expiresAt?: number;
    createdAt: number;
    updatedAt: number;
    author: {
      username: string;
      avatar?: string;
    } | null;
  };
  onEdit?: (announcement: any) => void;
  showActions?: boolean;
}

const typeStyles = {
  info: 'border-l-blue-500 bg-blue-50',
  warning: 'border-l-yellow-500 bg-yellow-50',
  urgent: 'border-l-red-500 bg-red-50',
  celebration: 'border-l-green-500 bg-green-50',
};

const typeIcons = {
  info: '📢',
  warning: '⚠️',
  urgent: '🚨',
  celebration: '🎉',
};

export function AnnouncementCard({ announcement, onEdit, showActions = true }: AnnouncementCardProps) {
  const { canEditAnnouncements, canDeleteAnnouncements } = usePermissions();
  const deleteAnnouncement = useMutation(api.announcements.deleteAnnouncement);
  const togglePin = useMutation(api.announcements.togglePin);

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this announcement?')) return;
    
    try {
      await deleteAnnouncement({ id: announcement._id });
    } catch (error) {
      console.error('Failed to delete announcement:', error);
      alert('Failed to delete announcement');
    }
  };

  const handleTogglePin = async () => {
    try {
      await togglePin({ id: announcement._id });
    } catch (error) {
      console.error('Failed to toggle pin:', error);
      alert('Failed to toggle pin status');
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isExpired = announcement.expiresAt && announcement.expiresAt < Date.now();

  return (
    <div className={`border-l-4 p-4 rounded-r-lg shadow-sm relative ${
      typeStyles[announcement.type as keyof typeof typeStyles] || typeStyles.info
    } ${isExpired ? 'opacity-60' : ''}`}>
      
      {/* Pin indicator */}
      {announcement.isPinned && (
        <div className="absolute top-2 right-2">
          <span className="text-yellow-500 text-lg">📌</span>
        </div>
      )}

      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className="text-lg">
            {typeIcons[announcement.type as keyof typeof typeIcons] || typeIcons.info}
          </span>
          <h3 className="text-lg font-semibold text-gray-900">
            {announcement.title}
          </h3>
          {isExpired && (
            <span className="px-2 py-1 text-xs bg-gray-200 text-gray-600 rounded-full">
              Expired
            </span>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="mb-3">
        <p className="text-gray-700 whitespace-pre-wrap">{announcement.content}</p>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-1">
            {announcement.author?.avatar && (
              <img 
                src={announcement.author.avatar} 
                alt={announcement.author.username}
                className="w-4 h-4 rounded-full"
              />
            )}
            <span>By {announcement.author?.username || 'Unknown'}</span>
          </div>
          <span>•</span>
          <span>{formatDate(announcement.createdAt)}</span>
          {announcement.expiresAt && (
            <>
              <span>•</span>
              <span>Expires {formatDate(announcement.expiresAt)}</span>
            </>
          )}
        </div>

        {/* Actions */}
        {showActions && (canEditAnnouncements() || canDeleteAnnouncements()) && (
          <div className="flex items-center space-x-2">
            {canEditAnnouncements() && (
              <>
                <button
                  onClick={handleTogglePin}
                  className="text-gray-400 hover:text-yellow-600 transition-colors"
                  title={announcement.isPinned ? 'Unpin' : 'Pin'}
                >
                  📌
                </button>
                <button
                  onClick={() => onEdit?.(announcement)}
                  className="text-gray-400 hover:text-blue-600 transition-colors"
                  title="Edit"
                >
                  ✏️
                </button>
              </>
            )}
            {canDeleteAnnouncements() && (
              <button
                onClick={handleDelete}
                className="text-gray-400 hover:text-red-600 transition-colors"
                title="Delete"
              >
                🗑️
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}