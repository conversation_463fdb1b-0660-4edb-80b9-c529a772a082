import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
  },
  build: {
    // Enable code splitting for better caching
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunk for third-party libraries
          vendor: ['react', 'react-dom'],
          // Convex chunk for Convex-related code
          convex: ['convex/react', 'convex/react-clerk'],
          // Auth chunk for authentication
          auth: ['@clerk/clerk-react'],
          // Router chunk for routing
          router: ['@tanstack/react-router'],
          // UI chunk for UI components
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select', '@radix-ui/react-toast'],
        },
      },
    },
    // Enable tree shaking
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'convex/react',
      'convex/react-clerk',
      '@clerk/clerk-react',
      '@tanstack/react-router',
    ],
  },
})
