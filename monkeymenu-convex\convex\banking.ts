import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import { 
  getCurrentUser, 
  requireAuth, 
  requireBankingPermission, 
  requireAdminPermission,
  canAccessBanking,
  canManageBanking 
} from "./lib/permissions";
import { 
  validateAmount, 
  validateCurrency, 
  validateWithdrawalStatus,
  sanitizeReference,
  validateDiscordId,
  validateDiscordTag
} from "./lib/validation";

// Get user account by currency
export const getUserAccount = query({
  args: { 
    userId: v.id("users"),
    currency: v.string() 
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("currency"), args.currency))
      .first();
  },
});

// Get all user accounts
export const getUserAccounts = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();
  },
});

// Create account
export const createAccount = mutation({
  args: {
    userId: v.id("users"),
    currency: v.string(),
    initialBalance: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    return await ctx.db.insert("accounts", {
      userId: args.userId,
      balance: args.initialBalance ?? 0,
      currency: args.currency,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Update account balance
export const updateAccountBalance = mutation({
  args: {
    accountId: v.id("accounts"),
    newBalance: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.patch(args.accountId, {
      balance: args.newBalance,
      updatedAt: Date.now(),
    });
  },
});

// Create transaction
export const createTransaction = mutation({
  args: {
    fromAccountId: v.optional(v.id("accounts")),
    toAccountId: v.optional(v.id("accounts")),
    amount: v.number(),
    currency: v.string(),
    type: v.string(),
    reference: v.optional(v.string()),
    metadata: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Create the transaction
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: args.fromAccountId,
      toAccountId: args.toAccountId,
      amount: args.amount,
      currency: args.currency,
      type: args.type,
      status: "pending",
      reference: args.reference,
      metadata: args.metadata,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Get detailed transaction history with pagination
export const getTransactionHistory = query({
  args: {
    userId: v.optional(v.id("users")),
    currency: v.optional(v.string()),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    let query = ctx.db.query("transactions");
    
    // Get user's accounts if userId is specified
    let accountIds: string[] = [];
    if (args.userId) {
      const accounts = await ctx.db
        .query("accounts")
        .withIndex("by_user", (q: any) => q.eq("userId", args.userId!))
        .collect();
      accountIds = accounts.map(acc => acc._id);
    }

    // Build query based on filters
    const transactions = await query.order("desc").collect();
    
    let filteredTransactions = transactions;

    // Filter by user accounts if specified
    if (args.userId && accountIds.length > 0) {
      filteredTransactions = filteredTransactions.filter(tx => 
        (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
        (tx.toAccountId && accountIds.includes(tx.toAccountId))
      );
    }

    // Apply other filters
    if (args.currency) {
      filteredTransactions = filteredTransactions.filter(tx => tx.currency === args.currency);
    }
    if (args.type) {
      filteredTransactions = filteredTransactions.filter(tx => tx.type === args.type);
    }
    if (args.status) {
      filteredTransactions = filteredTransactions.filter(tx => tx.status === args.status);
    }

    // Apply pagination
    const offset = args.offset ?? 0;
    const limit = args.limit ?? 20;
    const paginatedTransactions = filteredTransactions.slice(offset, offset + limit);

    // Enrich with account and user data
    const enrichedTransactions = await Promise.all(
      paginatedTransactions.map(async (tx) => {
        let fromAccount = null;
        let toAccount = null;
        let fromUser = null;
        let toUser = null;

        if (tx.fromAccountId) {
          fromAccount = await ctx.db.get(tx.fromAccountId);
          if (fromAccount) {
            fromUser = await ctx.db.get(fromAccount.userId);
          }
        }

        if (tx.toAccountId) {
          toAccount = await ctx.db.get(tx.toAccountId);
          if (toAccount) {
            toUser = await ctx.db.get(toAccount.userId);
          }
        }

        return {
          ...tx,
          fromAccount,
          toAccount,
          fromUser,
          toUser,
        };
      })
    );

    return {
      transactions: enrichedTransactions,
      total: filteredTransactions.length,
      hasMore: offset + limit < filteredTransactions.length,
    };
  },
});

// Get banking statistics for dashboard
export const getBankingStats = query({
  args: {
    userId: v.optional(v.id("users")),
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    const days = args.days ?? 30;
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    let accountIds: string[] = [];
    if (args.userId) {
      const accounts = await ctx.db
        .query("accounts")
        .withIndex("by_user", (q: any) => q.eq("userId", args.userId!))
        .collect();
      accountIds = accounts.map(acc => acc._id);
    }

    // Get transactions in time range
    let transactions = await ctx.db
      .query("transactions")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    // Filter by user if specified
    if (args.userId && accountIds.length > 0) {
      transactions = transactions.filter(tx => 
        (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
        (tx.toAccountId && accountIds.includes(tx.toAccountId))
      );
    }

    // Calculate statistics
    const stats = {
      totalTransactions: transactions.length,
      totalDeposits: 0,
      totalWithdrawals: 0,
      totalTransfers: 0,
      depositAmount: 0,
      withdrawalAmount: 0,
      transferAmount: 0,
      pendingWithdrawals: 0,
      completedWithdrawals: 0,
      cancelledWithdrawals: 0,
    };

    transactions.forEach(tx => {
      switch (tx.type) {
        case 'deposit':
          stats.totalDeposits++;
          stats.depositAmount += tx.amount;
          break;
        case 'withdrawal':
          stats.totalWithdrawals++;
          stats.withdrawalAmount += tx.amount;
          if (tx.status === 'completed') stats.completedWithdrawals++;
          break;
        case 'transfer':
          stats.totalTransfers++;
          stats.transferAmount += tx.amount;
          break;
      }
    });

    // Get withdrawal request stats
    let withdrawalRequests = await ctx.db
      .query("withdrawalRequests")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    if (args.userId) {
      withdrawalRequests = withdrawalRequests.filter(req => req.userId === args.userId);
    }

    withdrawalRequests.forEach(req => {
      switch (req.status) {
        case 'PENDING':
          stats.pendingWithdrawals++;
          break;
        case 'CANCELLED':
          stats.cancelledWithdrawals++;
          break;
      }
    });

    return stats;
  },
});

// Get user transactions
export const getUserTransactions = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number()) 
  },
  handler: async (ctx, args) => {
    // Get user accounts first
    const accounts = await ctx.db
      .query("accounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();
    
    const accountIds = accounts.map(acc => acc._id);
    
    // Get transactions involving these accounts
    const transactions = await ctx.db
      .query("transactions")
      .order("desc")
      .collect();
    
    // Filter transactions that involve user's accounts
    const userTransactions = transactions.filter(tx => 
      (tx.fromAccountId && accountIds.includes(tx.fromAccountId)) ||
      (tx.toAccountId && accountIds.includes(tx.toAccountId))
    );

    return userTransactions.slice(0, args.limit ?? 50);
  },
});

// Create or get user's cash account
export const getOrCreateCashAccount = mutation({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    // Check if account already exists
    const existingAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (existingAccount) {
      return existingAccount;
    }

    // Create new cash account
    const now = Date.now();
    const accountId = await ctx.db.insert("accounts", {
      userId: args.userId,
      balance: 0,
      currency: "cash",
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    return await ctx.db.get(accountId);
  },
});

// Create withdrawal request
export const createWithdrawalRequest = mutation({
  args: {
    amount: v.number(),
    discordMessageId: v.optional(v.string()),
    initiatedByDiscordId: v.optional(v.string()),
    initiatedByDiscordTag: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await getCurrentUser(ctx);
    
    if (!(await canAccessBanking(ctx))) {
      throw new ConvexError("Banking access required");
    }

    // Validate input
    validateAmount(args.amount);
    validateDiscordId(args.initiatedByDiscordId);
    validateDiscordTag(args.initiatedByDiscordTag);
    
    // Rate limiting: Check if user has pending requests or made request within last minute
    const oneMinuteAgo = Date.now() - 60 * 1000;
    const recentRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.gte(q.field("createdAt"), oneMinuteAgo))
      .collect();

    if (recentRequests.length > 0) {
      throw new ConvexError("Rate limit: Please wait before making another withdrawal request");
    }

    // Check for pending requests
    const pendingRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .filter((q) => q.eq(q.field("status"), "PENDING"))
      .collect();

    if (pendingRequests.length > 0) {
      throw new ConvexError("You already have a pending withdrawal request");
    }

    // Check total withdrawal count (max 50 per user)
    const totalRequests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", user._id))
      .collect();

    if (totalRequests.length >= 50) {
      throw new ConvexError("Maximum withdrawal limit reached (50 requests per user)");
    }

    // Validate amount
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    const now = Date.now();
    return await ctx.db.insert("withdrawalRequests", {
      userId: user._id,
      amount: args.amount,
      status: "PENDING",
      discordMessageId: args.discordMessageId,
      initiatedByDiscordId: args.initiatedByDiscordId,
      initiatedByDiscordTag: args.initiatedByDiscordTag,
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Get user's withdrawal requests
export const getUserWithdrawalRequests = query({
  args: { 
    userId: v.id("users"),
    limit: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .order("desc")
      .take(args.limit ?? 20);
  },
});

// Get all pending withdrawal requests (admin)
export const getPendingWithdrawalRequests = query({
  args: {},
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    const requests = await ctx.db
      .query("withdrawalRequests")
      .withIndex("by_status", (q) => q.eq("status", "PENDING"))
      .order("desc")
      .collect();

    // Join with user data
    const requestsWithUsers = await Promise.all(
      requests.map(async (request) => {
        const user = await ctx.db.get(request.userId);
        return { ...request, user };
      })
    );

    return requestsWithUsers;
  },
});

// Update withdrawal request status (admin)
export const updateWithdrawalRequestStatus = mutation({
  args: {
    requestId: v.id("withdrawalRequests"),
    status: v.string(),
    processedByBotDiscordId: v.optional(v.string()),
    processedByBotDiscordTag: v.optional(v.string()),
    transactionId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    const user = await getCurrentUser(ctx);
    const now = Date.now();
    const updateData: any = {
      status: args.status,
      updatedAt: now,
    };

    updateData.processedById = user._id;
    updateData.processedAt = now;

    if (args.processedByBotDiscordId) {
      updateData.processedByBotDiscordId = args.processedByBotDiscordId;
    }

    if (args.processedByBotDiscordTag) {
      updateData.processedByBotDiscordTag = args.processedByBotDiscordTag;
    }

    if (args.transactionId) {
      updateData.transactionId = args.transactionId;
    }

    return await ctx.db.patch(args.requestId, updateData);
  },
});

// Get user's cash balance
export const getUserCashBalance = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    return account?.balance ?? 0;
  },
});

// Update user's cash balance (from Torn API sync)
export const updateUserCashBalance = mutation({
  args: {
    userId: v.id("users"),
    newBalance: v.number(),
  },
  handler: async (ctx, args) => {
    // Validate input
    validateAmount(args.newBalance);
    
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      // Create new account if it doesn't exist
      const now = Date.now();
      return await ctx.db.insert("accounts", {
        userId: args.userId,
        balance: args.newBalance,
        currency: "cash",
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
    }

    return await ctx.db.patch(account._id, {
      balance: args.newBalance,
      updatedAt: Date.now(),
    });
  },
});

// Process withdrawal transaction (complete the withdrawal flow)
export const processWithdrawalTransaction = mutation({
  args: {
    withdrawalRequestId: v.id("withdrawalRequests"),
    tornTransactionId: v.string(),
    processedById: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    // Get the withdrawal request
    const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    if (withdrawalRequest.status !== "ACCEPTED") {
      throw new ConvexError("Withdrawal request must be ACCEPTED to process transaction");
    }

    // Get user's cash account
    const account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", withdrawalRequest.userId).eq("currency", "cash")
      )
      .first();

    if (!account) {
      throw new ConvexError("User cash account not found");
    }

    // Create transaction record
    const now = Date.now();
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: account._id,
      amount: withdrawalRequest.amount,
      currency: "cash",
      type: "withdrawal",
      status: "completed",
      reference: args.tornTransactionId,
      withdrawalRequestId: args.withdrawalRequestId,
      metadata: {
        processedById: args.processedById,
        tornTransactionId: args.tornTransactionId,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    // Update withdrawal request status
    await ctx.db.patch(args.withdrawalRequestId, {
      status: "COMPLETED",
      transactionId: tornTransactionId,
      processedById: args.processedById,
      processedAt: now,
      updatedAt: now,
    });

    // Update account balance
    await ctx.db.patch(account._id, {
      balance: account.balance - withdrawalRequest.amount,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Cancel withdrawal request
export const cancelWithdrawalRequest = mutation({
  args: {
    withdrawalRequestId: v.id("withdrawalRequests"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const withdrawalRequest = await ctx.db.get(args.withdrawalRequestId);
    if (!withdrawalRequest) {
      throw new ConvexError("Withdrawal request not found");
    }

    if (withdrawalRequest.status === "COMPLETED" || withdrawalRequest.status === "CANCELLED") {
      throw new ConvexError("Cannot cancel a completed or already cancelled request");
    }

    const now = Date.now();
    
    // Update withdrawal request
    await ctx.db.patch(args.withdrawalRequestId, {
      status: "CANCELLED",
      updatedAt: now,
      processedAt: now,
    });

    // Create transaction record for cancellation
    if (withdrawalRequest.status === "ACCEPTED") {
      await ctx.db.insert("transactions", {
        amount: withdrawalRequest.amount,
        currency: "cash",
        type: "withdrawal_cancelled",
        status: "completed",
        withdrawalRequestId: args.withdrawalRequestId,
        metadata: {
          reason: args.reason || "Request cancelled",
          originalStatus: withdrawalRequest.status,
        },
        processedAt: now,
        createdAt: now,
        updatedAt: now,
      });
    }

    return true;
  },
});

// Add funds to user account (admin function)
export const addFundsToAccount = mutation({
  args: {
    userId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    reference: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    const adminUser = await getCurrentUser(ctx);
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    // Get or create account
    let account = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.userId).eq("currency", args.currency)
      )
      .first();

    const now = Date.now();

    if (!account) {
      // Create new account
      const accountId = await ctx.db.insert("accounts", {
        userId: args.userId,
        balance: args.amount,
        currency: args.currency,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      account = await ctx.db.get(accountId);
    } else {
      // Update existing account
      await ctx.db.patch(account._id, {
        balance: account.balance + args.amount,
        updatedAt: now,
      });
    }

    // Create transaction record
    const transactionId = await ctx.db.insert("transactions", {
      toAccountId: account!._id,
      amount: args.amount,
      currency: args.currency,
      type: "deposit",
      status: "completed",
      reference: args.reference,
      metadata: {
        addedById: adminUser._id,
        reason: "Admin added funds",
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});

// Transfer funds between accounts
export const transferFunds = mutation({
  args: {
    fromUserId: v.id("users"),
    toUserId: v.id("users"),
    amount: v.number(),
    currency: v.string(),
    reference: v.optional(v.string()),
    initiatedById: v.id("users"),
  },
  handler: async (ctx, args) => {
    await requireBankingPermission(ctx);
    
    if (args.amount <= 0) {
      throw new ConvexError("Amount must be greater than 0");
    }

    // Get source account
    const fromAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.fromUserId).eq("currency", args.currency)
      )
      .first();

    if (!fromAccount) {
      throw new ConvexError("Source account not found");
    }

    if (fromAccount.balance < args.amount) {
      throw new ConvexError("Insufficient funds");
    }

    // Get or create destination account
    let toAccount = await ctx.db
      .query("accounts")
      .withIndex("by_user_currency", (q) => 
        q.eq("userId", args.toUserId).eq("currency", args.currency)
      )
      .first();

    const now = Date.now();

    if (!toAccount) {
      // Create destination account
      const accountId = await ctx.db.insert("accounts", {
        userId: args.toUserId,
        balance: 0,
        currency: args.currency,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      toAccount = await ctx.db.get(accountId);
    }

    // Update balances
    await ctx.db.patch(fromAccount._id, {
      balance: fromAccount.balance - args.amount,
      updatedAt: now,
    });

    await ctx.db.patch(toAccount!._id, {
      balance: toAccount!.balance + args.amount,
      updatedAt: now,
    });

    // Create transaction record
    const transactionId = await ctx.db.insert("transactions", {
      fromAccountId: fromAccount._id,
      toAccountId: toAccount!._id,
      amount: args.amount,
      currency: args.currency,
      type: "transfer",
      status: "completed",
      reference: args.reference,
      metadata: {
        initiatedById: args.initiatedById,
      },
      processedAt: now,
      createdAt: now,
      updatedAt: now,
    });

    return transactionId;
  },
});