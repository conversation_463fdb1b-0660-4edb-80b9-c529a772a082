import { QueryCtx, MutationCtx } from "../_generated/server";
import { ConvexError } from "convex/values";

export async function requireAuth(ctx: QueryCtx | MutationCtx): Promise<string> {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new ConvexError("Authentication required");
  }
  return identity.subject;
}

export async function getCurrentUser(ctx: QueryCtx | MutationCtx) {
  const clerkId = await requireAuth(ctx);
  
  const user = await ctx.db
    .query("users")
    .withIndex("by_clerk_id", (q) => q.eq("clerkId", clerkId))
    .first();

  if (!user) {
    throw new ConvexError("User not found");
  }

  return user;
}

export async function requirePermission(
  ctx: QueryCtx | MutationCtx, 
  permission: string
): Promise<void> {
  const user = await getCurrentUser(ctx);
  
  if (!user.permissions.includes(permission) && !user.permissions.includes("admin.all")) {
    throw new ConvexError(`Permission required: ${permission}`);
  }
}

export async function requireBankingPermission(
  ctx: QueryCtx | MutationCtx
): Promise<void> {
  await requirePermission(ctx, "banking.manage");
}

export async function requireAdminPermission(
  ctx: QueryCtx | MutationCtx
): Promise<void> {
  await requirePermission(ctx, "admin.all");
}

export async function canAccessBanking(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("banking.view") || 
           user.permissions.includes("banking.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canManageBanking(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("banking.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canCreateAnnouncements(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("announcements.create") ||
           user.permissions.includes("announcements.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canEditAnnouncements(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("announcements.edit") ||
           user.permissions.includes("announcements.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canDeleteAnnouncements(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("announcements.delete") ||
           user.permissions.includes("announcements.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canManageAnnouncements(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("announcements.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canViewTargets(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("targets.view") ||
           user.permissions.includes("targets.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canUpdateTargets(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("targets.update") ||
           user.permissions.includes("targets.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canDeleteTargets(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("targets.delete") ||
           user.permissions.includes("targets.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canManageTargets(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("targets.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canViewWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.view") ||
           user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canCreateWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.create") ||
           user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canEditWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.edit") ||
           user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canUpdateWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.update") ||
           user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canDeleteWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.delete") ||
           user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}

export async function canManageWars(
  ctx: QueryCtx | MutationCtx
): Promise<boolean> {
  try {
    const user = await getCurrentUser(ctx);
    return user.permissions.includes("wars.manage") ||
           user.permissions.includes("admin.all");
  } catch {
    return false;
  }
}