import React from 'react';

interface AccountBalanceProps {
  balance: number;
  loading?: boolean;
}

export function AccountBalance({ balance, loading = false }: AccountBalanceProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', '$');
  };

  if (loading) {
    return (
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium mb-2">Cash Balance</h3>
            <div className="h-8 bg-blue-400 rounded animate-pulse w-32"></div>
          </div>
          <div className="text-4xl">💰</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white shadow-lg">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium mb-2 opacity-90">Cash Balance</h3>
          <div className="text-3xl font-bold">
            {formatCurrency(balance)}
          </div>
          <p className="text-sm opacity-75 mt-1">Available for withdrawal</p>
        </div>
        <div className="text-4xl opacity-80">💰</div>
      </div>
    </div>
  );
}