import { action } from "../_generated/server";
import { v } from "convex/values";
import { internal } from "../_generated/api";

// Handle balance command
export const handleBalanceCommand = action({
  args: {
    userId: v.id("users"),
    discordId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Get user's accounts
      const accounts = await ctx.runQuery(internal.banking.getUserAccounts, {
        userId: args.userId,
      });

      if (!accounts || accounts.length === 0) {
        return {
          success: true,
          message: "💰 You don't have any active accounts yet.",
        };
      }

      // Format balance information
      const balanceInfo = accounts
        .map(account => {
          const currency = account.currency === "cash" ? "💵" : "🪙";
          return `${currency} **${account.currency.toUpperCase()}**: $${account.balance.toLocaleString()}`;
        })
        .join("\n");

      return {
        success: true,
        message: `💰 **Your Account Balances:**\n${balanceInfo}`,
        embed: {
          title: "💰 Account Balance",
          description: balanceInfo,
          color: 0x00ff00,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving balance information.",
      };
    }
  },
});

// Handle withdraw command
export const handleWithdrawCommand = action({
  args: {
    userId: v.id("users"),
    discordId: v.string(),
    amount: v.number(),
  },
  handler: async (ctx, args) => {
    try {
      if (args.amount <= 0) {
        return {
          success: false,
          message: "❌ Please specify a valid amount to withdraw.",
        };
      }

      // Create withdrawal request
      const requestId = await ctx.runMutation(internal.banking.createWithdrawalRequest, {
        userId: args.userId,
        amount: args.amount,
        initiatedByDiscordId: args.discordId,
      });

      return {
        success: true,
        message: `✅ Withdrawal request created for $${args.amount.toLocaleString()}!\nRequest ID: ${requestId}\n\nYour request is pending approval.`,
        embed: {
          title: "💸 Withdrawal Request Created",
          description: `Successfully created withdrawal request for $${args.amount.toLocaleString()}`,
          color: 0x0099ff,
          fields: [
            {
              name: "Amount",
              value: `$${args.amount.toLocaleString()}`,
              inline: true,
            },
            {
              name: "Status",
              value: "Pending Approval",
              inline: true,
            },
          ],
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: `❌ Error creating withdrawal request: ${error instanceof Error ? error.message : "Unknown error"}`,
      };
    }
  },
});

// Handle targets command
export const handleTargetsCommand = action({
  args: {
    userId: v.id("users"),
    filters: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Parse filters from command arguments
      const parsedFilters: any = {};
      
      for (let i = 0; i < args.filters.length; i += 2) {
        const key = args.filters[i];
        const value = args.filters[i + 1];
        
        if (key && value) {
          switch (key.toLowerCase()) {
            case "level":
            case "lvl":
              const [min, max] = value.split("-").map(Number);
              if (!isNaN(min)) parsedFilters.levelMin = min;
              if (!isNaN(max)) parsedFilters.levelMax = max;
              break;
            case "status":
              parsedFilters.status = value.toLowerCase();
              break;
            case "faction":
              parsedFilters.faction = value;
              break;
            case "respect":
              const [minResp, maxResp] = value.split("-").map(Number);
              if (!isNaN(minResp)) parsedFilters.respectMin = minResp;
              if (!isNaN(maxResp)) parsedFilters.respectMax = maxResp;
              break;
          }
        }
      }

      // Get targets with filters
      const targets = await ctx.runQuery(internal.targets.getTargets, {
        ...parsedFilters,
        limit: 10, // Limit for Discord display
      });

      if (!targets || targets.length === 0) {
        return {
          success: true,
          message: "🎯 No targets found matching your criteria.",
        };
      }

      // Format targets for Discord
      const targetList = targets
        .slice(0, 5) // Show top 5 in message
        .map((target, index) => {
          const statusEmoji = target.status === "active" ? "🟢" : 
                             target.status === "hospitalized" ? "🏥" : 
                             target.status === "jailed" ? "🔒" : "⚫";
          
          return `${index + 1}. ${statusEmoji} **${target.username}** [${target.tornId}]\n` +
                 `   Level: ${target.level} | Respect: ${target.respect || "N/A"}`;
        })
        .join("\n\n");

      return {
        success: true,
        message: `🎯 **Top Targets** (${targets.length} found):\n\n${targetList}`,
        embed: {
          title: "🎯 Target Search Results",
          description: targetList,
          color: 0xffa500,
          footer: {
            text: `Showing ${Math.min(5, targets.length)} of ${targets.length} targets`,
          },
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving targets.",
      };
    }
  },
});

// Handle war command
export const handleWarCommand = action({
  args: {
    userId: v.id("users"),
    subcommand: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const subcommand = args.subcommand?.toLowerCase() || "active";

      switch (subcommand) {
        case "active":
        case "current":
          const activeWars = await ctx.runQuery(internal.wars.getActiveWars, {});
          
          if (!activeWars || activeWars.length === 0) {
            return {
              success: true,
              message: "⚔️ No active wars at the moment.",
            };
          }

          const warInfo = activeWars
            .map(war => {
              const duration = Math.floor((Date.now() - war.startTime) / (1000 * 60));
              const hours = Math.floor(duration / 60);
              const minutes = duration % 60;
              const durationText = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
              
              return `⚔️ **${war.factionName}** vs **${war.enemyFactionName}**\n` +
                     `   Score: ${war.ourScore} - ${war.enemyScore}\n` +
                     `   Duration: ${durationText}`;
            })
            .join("\n\n");

          return {
            success: true,
            message: `⚔️ **Active Wars:**\n\n${warInfo}`,
            embed: {
              title: "⚔️ Active Wars",
              description: warInfo,
              color: 0xff0000,
              timestamp: new Date().toISOString(),
            },
          };

        case "stats":
        case "statistics":
          // Get stats for the most recent active war
          if (activeWars && activeWars.length > 0) {
            const stats = await ctx.runQuery(internal.wars.getWarStats, {
              warId: activeWars[0]._id,
            });

            if (stats) {
              const statsText = `**${stats.war.factionName}** vs **${stats.war.enemyFactionName}**\n\n` +
                               `📊 **Statistics:**\n` +
                               `• Total Attacks: ${stats.totalAttacks}\n` +
                               `• Wins: ${stats.wins} (${stats.winRate}%)\n` +
                               `• Losses: ${stats.losses}\n` +
                               `• Total Respect: ${stats.totalRespect.toLocaleString()}\n` +
                               `• Avg Respect: ${stats.averageRespect}`;

              return {
                success: true,
                message: statsText,
                embed: {
                  title: "📊 War Statistics",
                  description: statsText,
                  color: 0x9932cc,
                  timestamp: new Date().toISOString(),
                },
              };
            }
          }

          return {
            success: true,
            message: "📊 No war statistics available.",
          };

        default:
          return {
            success: false,
            message: "❌ Unknown war subcommand. Use `active` or `stats`.",
          };
      }
    } catch (error) {
      return {
        success: false,
        message: "❌ Error retrieving war information.",
      };
    }
  },
});

// Handle help command
export const handleHelpCommand = action({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const helpText = `🤖 **MonkeyMenu Bot Commands:**

**💰 Banking:**
• \`!balance\` - Check your account balances
• \`!withdraw <amount>\` - Request a withdrawal

**🎯 Targets:**
• \`!targets\` - Show available targets
• \`!targets level 1-50\` - Filter by level range
• \`!targets status active\` - Filter by status

**⚔️ Wars:**
• \`!war\` or \`!war active\` - Show active wars
• \`!war stats\` - Show war statistics  

**🔗 Account:**
• \`!link\` - Check account linking status
• \`!help\` - Show this help message

**Examples:**
\`!targets level 20-40 status active\`
\`!withdraw 50000\``;

    return {
      success: true,
      message: helpText,
      embed: {
        title: "🤖 MonkeyMenu Bot Help",
        description: helpText,
        color: 0x0099ff,
        timestamp: new Date().toISOString(),
      },
    };
  },
});